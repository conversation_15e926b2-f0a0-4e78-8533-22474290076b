import uuid
from typing import List, Dict, Any
from ..data_models.v2_models import AtomicConstraint

class SchemaValidationError(Exception):
    pass

class PrevalidationError(Exception):
    pass

class ConstraintPreprocessor:
    """
    Engine for standardizing and pre-validating proposed constraints
    to prevent conflicts at the source.
    Based on: docs/features/T001-create-plans-20250612/v4/design/化简/核心/design/v2/4-微观图构建与双重验证引擎-V2.md
    """

    def __init__(self):
        # In a real scenario, this would be a connection to a database or a shared cache.
        self.knowledge_base: List[AtomicConstraint] = []
        self._id_counter = 0

    def _generate_constraint_id(self, category: str) -> str:
        """Generates a unique, categorized ID for a new constraint."""
        prefix_map = {
            'constraint': 'GC',
            'guardrail': 'GG',
            'boundary_condition': 'GB',
            'state_machine': 'GS'
        }
        prefix = prefix_map.get(category, 'GN') # GN for General
        self._id_counter += 1
        return f"{prefix}{self._id_counter:03d}"

    def _validate_schema(self, proposed_constraint: Dict[str, Any]):
        """
        Validates the proposed constraint against a predefined schema
        based on its category and type.
        (This is a simplified placeholder for a real schema validation logic, e.g., using JSONSchema)
        """
        required_fields = ['category', 'type', 'params', 'description']
        for field in required_fields:
            if field not in proposed_constraint:
                raise SchemaValidationError(f"Missing required field: '{field}'")
        
        # Example of a category-specific schema check
        if proposed_constraint['category'] == 'boundary_condition':
            if 'max_ms' not in proposed_constraint['params']:
                 raise SchemaValidationError("Boundary condition of type 'response_time' requires 'max_ms' in params.")
        return True

    def preprocess_and_validate(self, proposed_constraint: Dict[str, Any]) -> AtomicConstraint:
        """
        Processes a single proposed constraint. If it passes validation,
        it gets a unique ID and is returned as a full AtomicConstraint object.
        If it fails, it raises an error.

        Args:
            proposed_constraint: A dictionary representing the new constraint to be added.

        Returns:
            A validated AtomicConstraint object.

        Raises:
            SchemaValidationError: If the proposal does not match the required structure.
            PrevalidationError: If the proposal conflicts with existing constraints.
        """
        # 1. Validate the proposal's structure (Schema Validation)
        self._validate_schema(proposed_constraint)

        # 2. Perform deterministic conflict validation
        # This is where the core logic of conflict prevention resides.
        # Example: A new 'max_memory' constraint cannot be less than an existing 'min_memory'.
        if proposed_constraint['type'] == 'response_time':
            new_max_ms = proposed_constraint['params'].get('max_ms')
            if new_max_ms is not None:
                for existing_constraint in self.knowledge_base:
                    # This is a very simplified example. A real engine would have more complex rules.
                    if existing_constraint.type == 'response_time' and 'min_ms' in existing_constraint.params:
                        if new_max_ms < existing_constraint.params['min_ms']:
                            raise PrevalidationError(
                                f"Pre-validation failed: Proposed max_ms ({new_max_ms}) conflicts with "
                                f"existing min_ms ({existing_constraint.params['min_ms']}) from constraint {existing_constraint.id}."
                            )

        # 3. If all validations pass, assign an ID and create the final object
        new_id = self._generate_constraint_id(proposed_constraint['category'])
        
        final_constraint = AtomicConstraint(
            id=new_id,
            category=proposed_constraint['category'],
            type=proposed_constraint['type'],
            params=proposed_constraint['params'],
            description=proposed_constraint['description'],
            parent_id=proposed_constraint.get('parent_id'),
            source_block_id=proposed_constraint.get('source_block_id')
        )

        # Add to knowledge base after successful validation
        self.knowledge_base.append(final_constraint)
        
        return final_constraint

# Example Usage (for demonstration and testing)
if __name__ == '__main__':
    preprocessor = ConstraintPreprocessor()

    # --- Simulation of Phase Zero ---
    print("--- Running Phase Zero Simulation ---")

    # 1. A 'min_response_time' constraint is proposed and added successfully.
    proposal_1 = {
        "category": "boundary_condition",
        "type": "response_time",
        "params": {"min_ms": 100},
        "description": "All APIs must respond in at least 100ms to prevent DoS."
    }
    try:
        validated_1 = preprocessor.preprocess_and_validate(proposal_1)
        print(f"✅ Successfully added constraint: {validated_1}")
    except (SchemaValidationError, PrevalidationError) as e:
        print(f"❌ Error adding constraint: {e}")

    # 2. A valid 'max_response_time' is proposed.
    proposal_2 = {
        "category": "boundary_condition",
        "type": "response_time",
        "params": {"max_ms": 1000},
        "description": "All APIs must respond within 1000ms."
    }
    try:
        validated_2 = preprocessor.preprocess_and_validate(proposal_2)
        print(f"✅ Successfully added constraint: {validated_2}")
    except (SchemaValidationError, PrevalidationError) as e:
        print(f"❌ Error adding constraint: {e}")

    # 3. A conflicting 'max_response_time' is proposed, which should fail.
    proposal_3_conflicting = {
        "category": "boundary_condition",
        "type": "response_time",
        "params": {"max_ms": 50}, # This conflicts with min_ms of 100
        "description": "A new, faster response time requirement."
    }
    print("\nAttempting to add a conflicting constraint...")
    try:
        validated_3 = preprocessor.preprocess_and_validate(proposal_3_conflicting)
        print(f"✅ Successfully added constraint: {validated_3}")
    except (SchemaValidationError, PrevalidationError) as e:
        print(f"❌ Error adding constraint: {e}")
        print("As expected, the pre-validation engine prevented the conflict at the source.")

    print("\n--- Final Knowledge Base ---")
    for constraint in preprocessor.knowledge_base:
        print(constraint)
