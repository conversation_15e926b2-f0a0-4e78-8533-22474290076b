/**
 * 应用管理器 - 统一管理所有组件的初始化和生命周期
 */
class AppManager {
    constructor(projectId, config = {}) {
        this.projectId = projectId;
        this.config = {
            autoInit: true,
            errorHandler: null,
            ...config
        };
        
        this.dataManager = null;
        this.components = new Map();
        this.isInitialized = false;
        this.initPromise = null;
        
        // 组件配置映射
        this.componentConfigs = new Map();
        
        // 绑定错误处理
        this.handleGlobalError = this.handleGlobalError.bind(this);
        this.setupGlobalErrorHandling();
    }
    
    /**
     * 注册组件配置
     * @param {string} type - 组件类型
     * @param {string} containerId - 容器ID
     * @param {class} ComponentClass - 组件类
     * @param {object} config - 组件配置
     */
    registerComponent(type, containerId, ComponentClass, config = {}) {
        this.componentConfigs.set(type, {
            containerId,
            ComponentClass,
            config: {
                ...config,
                type
            }
        });
        
        console.log(`Registered component: ${type} -> ${containerId}`);
    }
    
    /**
     * 批量注册组件
     * @param {Array} components - 组件配置数组
     */
    registerComponents(components) {
        components.forEach(({ type, containerId, ComponentClass, config }) => {
            this.registerComponent(type, containerId, ComponentClass, config);
        });
    }
    
    /**
     * 初始化应用
     * @returns {Promise} 初始化Promise
     */
    async init() {
        if (this.isInitialized) {
            console.warn('AppManager is already initialized');
            return this.initPromise;
        }
        
        if (this.initPromise) {
            return this.initPromise;
        }
        
        this.initPromise = this._doInit();
        return this.initPromise;
    }
    
    /**
     * 执行初始化
     * @private
     */
    async _doInit() {
        try {
            console.log(`Initializing AppManager for project: ${this.projectId}`);
            
            // 1. 初始化数据管理器
            await this.initDataManager();
            
            // 2. 初始化所有组件
            await this.initComponents();
            
            // 3. 设置组件间通信
            this.setupComponentCommunication();
            
            this.isInitialized = true;
            console.log('AppManager initialized successfully');
            
            // 触发初始化完成事件
            this.onInitialized();
            
        } catch (error) {
            console.error('Failed to initialize AppManager:', error);
            this.handleGlobalError(error);
            throw error;
        }
    }
    
    /**
     * 初始化数据管理器
     */
    async initDataManager() {
        console.log('Initializing DataManager...');
        this.dataManager = new DataManager(this.projectId);

        // 尝试等待WebSocket连接建立，但不阻塞初始化
        try {
            await this.waitForWebSocketConnection();
            console.log('DataManager initialized with WebSocket connection');
        } catch (error) {
            console.warn('WebSocket connection failed, continuing with HTTP-only mode:', error.message);
            console.log('DataManager initialized in HTTP-only mode');
        }
    }
    
    /**
     * 等待WebSocket连接
     */
    waitForWebSocketConnection(timeout = 5000) {
        return new Promise((resolve, reject) => {
            const checkConnection = () => {
                if (this.dataManager.wsClient && this.dataManager.wsClient.readyState === WebSocket.OPEN) {
                    resolve();
                } else if (this.dataManager.wsClient && this.dataManager.wsClient.readyState === WebSocket.CLOSED) {
                    reject(new Error('WebSocket connection failed'));
                } else {
                    setTimeout(checkConnection, 100);
                }
            };
            
            checkConnection();
            
            // 超时处理
            setTimeout(() => {
                reject(new Error('WebSocket connection timeout'));
            }, timeout);
        });
    }
    
    /**
     * 初始化所有组件
     */
    async initComponents() {
        console.log('Initializing components...');
        
        const initPromises = [];
        
        for (const [type, componentConfig] of this.componentConfigs) {
            const promise = this.initComponent(type, componentConfig);
            initPromises.push(promise);
        }
        
        // 并行初始化所有组件
        const results = await Promise.allSettled(initPromises);
        
        // 检查初始化结果
        const failed = results.filter(result => result.status === 'rejected');
        if (failed.length > 0) {
            console.warn(`${failed.length} components failed to initialize:`, failed);
        }
        
        const successful = results.filter(result => result.status === 'fulfilled');
        console.log(`${successful.length} components initialized successfully`);
    }
    
    /**
     * 初始化单个组件
     */
    async initComponent(type, componentConfig) {
        const { containerId, ComponentClass, config } = componentConfig;
        
        try {
            console.log(`Initializing component: ${type}`);
            
            // 检查容器是否存在
            const container = document.getElementById(containerId);
            if (!container) {
                throw new Error(`Container element '${containerId}' not found for component '${type}'`);
            }
            
            // 创建组件实例，传入appManager引用
            const component = new ComponentClass(containerId, this.dataManager, { ...config, appManager: this });
            
            // 初始化组件
            await component.init();
            
            // 存储组件实例
            this.components.set(type, component);
            
            console.log(`Component ${type} initialized successfully`);
            return component;
            
        } catch (error) {
            console.error(`Failed to initialize component ${type}:`, error);
            
            // 在容器中显示错误信息
            const container = document.getElementById(containerId);
            if (container) {
                container.innerHTML = `
                    <div style="
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        height: 100%;
                        color: #F44336;
                        text-align: center;
                        font-size: 0.9rem;
                        padding: 1rem;
                    ">
                        <div>
                            <div style="font-size: 1.5rem; margin-bottom: 0.5rem;">⚠️</div>
                            <div style="font-weight: bold; margin-bottom: 0.5rem;">组件加载失败</div>
                            <div style="font-size: 0.8rem; color: #888; margin-bottom: 1rem;">${error.message}</div>
                            <button onclick="window.appManager?.retryComponent('${type}')" style="
                                background: #0078D4;
                                color: white;
                                border: none;
                                padding: 0.5rem 1rem;
                                border-radius: 4px;
                                cursor: pointer;
                                font-size: 0.8rem;
                            ">重试</button>
                        </div>
                    </div>
                `;
            }
            
            throw error;
        }
    }
    
    /**
     * 设置组件间通信
     */
    setupComponentCommunication() {
        // 可以在这里设置组件间的事件通信
        console.log('Setting up component communication...');
        
        // 示例：监听特定事件并通知相关组件
        this.dataManager.subscribe('constraint_selected', (constraintId) => {
            const constraintComponent = this.getComponent('constraint');
            if (constraintComponent && typeof constraintComponent.selectConstraint === 'function') {
                constraintComponent.selectConstraint(constraintId);
            }
        });
    }
    
    /**
     * 获取组件实例
     * @param {string} type - 组件类型
     * @returns {BaseComponent} 组件实例
     */
    getComponent(type) {
        return this.components.get(type);
    }
    
    /**
     * 获取所有组件
     * @returns {Map} 组件映射
     */
    getAllComponents() {
        return new Map(this.components);
    }
    
    /**
     * 重试组件初始化
     * @param {string} type - 组件类型
     */
    async retryComponent(type) {
        const componentConfig = this.componentConfigs.get(type);
        if (!componentConfig) {
            console.error(`Component config not found for type: ${type}`);
            return;
        }
        
        try {
            // 销毁现有组件（如果存在）
            const existingComponent = this.components.get(type);
            if (existingComponent) {
                existingComponent.destroy();
                this.components.delete(type);
            }
            
            // 重新初始化组件
            await this.initComponent(type, componentConfig);
            
        } catch (error) {
            console.error(`Failed to retry component ${type}:`, error);
        }
    }
    
    /**
     * 刷新所有组件数据
     */
    async refreshAll() {
        console.log('Refreshing all components...');
        
        const refreshPromises = Array.from(this.components.values()).map(component => {
            if (typeof component.refresh === 'function') {
                return component.refresh().catch(error => {
                    console.error(`Failed to refresh component:`, error);
                });
            }
            return Promise.resolve();
        });
        
        await Promise.all(refreshPromises);
        console.log('All components refreshed');
    }
    
    /**
     * 设置全局错误处理
     */
    setupGlobalErrorHandling() {
        // 捕获未处理的Promise拒绝
        window.addEventListener('unhandledrejection', this.handleGlobalError);
        
        // 捕获JavaScript错误
        window.addEventListener('error', this.handleGlobalError);
    }
    
    /**
     * 处理全局错误
     */
    handleGlobalError(event) {
        let error;
        
        if (event.type === 'unhandledrejection') {
            error = event.reason;
        } else if (event.type === 'error') {
            error = event.error || new Error(event.message);
        } else {
            error = event;
        }
        
        console.error('Global error caught:', error);
        
        // 调用用户定义的错误处理器
        if (this.config.errorHandler && typeof this.config.errorHandler === 'function') {
            try {
                this.config.errorHandler(error);
            } catch (handlerError) {
                console.error('Error in error handler:', handlerError);
            }
        }
        
        // 显示用户友好的错误提示
        this.showErrorNotification(error);
    }
    
    /**
     * 显示错误通知
     */
    showErrorNotification(error) {
        // 创建错误通知元素
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #F44336;
            color: white;
            padding: 1rem;
            border-radius: 4px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            z-index: 10000;
            max-width: 400px;
            font-size: 0.9rem;
        `;
        
        notification.innerHTML = `
            <div style="display: flex; align-items: center; gap: 0.5rem;">
                <span style="font-size: 1.2rem;">⚠️</span>
                <div>
                    <div style="font-weight: bold; margin-bottom: 0.3rem;">发生错误</div>
                    <div style="font-size: 0.8rem; opacity: 0.9;">${error.message || '未知错误'}</div>
                </div>
                <button onclick="this.parentElement.parentElement.remove()" style="
                    background: none;
                    border: none;
                    color: white;
                    cursor: pointer;
                    font-size: 1.2rem;
                    margin-left: auto;
                ">×</button>
            </div>
        `;
        
        document.body.appendChild(notification);
        
        // 5秒后自动移除
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 5000);
    }
    
    /**
     * 初始化完成回调
     */
    onInitialized() {
        // 触发自定义事件
        const event = new CustomEvent('appManagerInitialized', {
            detail: { appManager: this }
        });
        window.dispatchEvent(event);
    }
    
    /**
     * 销毁应用
     */
    destroy() {
        console.log('Destroying AppManager...');
        
        // 销毁所有组件
        this.components.forEach(component => {
            try {
                component.destroy();
            } catch (error) {
                console.error('Error destroying component:', error);
            }
        });
        this.components.clear();
        
        // 销毁数据管理器
        if (this.dataManager) {
            this.dataManager.destroy();
            this.dataManager = null;
        }
        
        // 移除全局错误处理
        window.removeEventListener('unhandledrejection', this.handleGlobalError);
        window.removeEventListener('error', this.handleGlobalError);
        
        this.isInitialized = false;
        this.initPromise = null;
        
        console.log('AppManager destroyed');
    }
    
    /**
     * 获取应用状态
     */
    getStatus() {
        return {
            isInitialized: this.isInitialized,
            projectId: this.projectId,
            componentCount: this.components.size,
            components: Array.from(this.components.keys()),
            dataManagerConnected: this.dataManager?.wsClient?.readyState === WebSocket.OPEN
        };
    }
}

// 全局应用管理器实例
let appManager = null;

/**
 * 初始化应用
 * @param {string} projectId - 项目ID
 * @param {object} config - 配置选项
 * @returns {Promise<AppManager>} 应用管理器实例
 */
async function initializeApp(projectId, config = {}) {
    if (appManager) {
        console.warn('App is already initialized');
        return appManager;
    }
    
    try {
        appManager = new AppManager(projectId, config);
        
        // 将实例绑定到全局，供调试和错误恢复使用
        window.appManager = appManager;
        
        await appManager.init();
        return appManager;
        
    } catch (error) {
        console.error('Failed to initialize app:', error);
        appManager = null;
        window.appManager = null;
        throw error;
    }
}

/**
 * 获取当前应用管理器实例
 * @returns {AppManager} 应用管理器实例
 */
function getAppManager() {
    return appManager;
}

// 导出
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { AppManager, initializeApp, getAppManager };
} else {
    window.AppManager = AppManager;
    window.initializeApp = initializeApp;
    window.getAppManager = getAppManager;
}
