/**
 * 人类输入控制区 (HumanInputComponent)
 * 
 * 功能：
 * - 显示从其他组件传递过来的详细信息。
 * - 包含用户输入文本框。
 * - 提供开始、暂停、停止和扫描等核心控制按钮。
 * 
 * 数据依赖：
 * - control_status: 控制按钮的启用/禁用状态。
 */
class HumanInputComponent extends BaseComponent {
    getDataTypes() {
        return ['control_status'];
    }

    render() {
        const controlStatus = this.getData('control_status') || { start: false, pause: true, stop: true, scan: true };

        this.container.innerHTML = `
            <div class="area-content" style="margin-top: 1rem; height: calc(100% - 1rem); display: flex; flex-direction: column;">
                <!-- 详细区 -->
                <div id="detail-area" class="vscode-scrollbar" style="flex: 1; background: #2A2D30; border: 1px solid #3C3F41; border-radius: 4px; margin-bottom: 0.8rem; position: relative; overflow-y: auto;">
                    <div id="detail-title" style="position: absolute; top: 4px; left: 8px; background: #3C3F41; color: #BBBBBB; padding: 2px 6px; border-radius: 3px; font-size: 0.7rem; z-index: 10;">详细</div>
                    <div id="detail-content" style="padding: 0.5rem; font-family: monospace; font-size: 0.8rem; color: #BBBBBB; line-height: 1.3; height: 100%; display: flex; flex-direction: column;">
                        <div id="detail-placeholder" style="color: #666; text-align: center; flex: 1; display: flex; align-items: center; justify-content: center;">点击左侧算法思维日志查看详细内容</div>
                    </div>
                </div>

                <!-- 输入框 -->
                <textarea id="user-input" placeholder="基于详细区内容提问或自由输入..." style="width: 100%; height: 60px; background: #2A2D30; color: #BBBBBB; border: 1px solid #3C3F41; border-radius: 4px; padding: 0.5rem; resize: vertical; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; font-size: 0.9rem; margin-bottom: 0.8rem;"></textarea>

                <!-- 控制按钮 -->
                <div class="control-buttons" style="display: flex; gap: 0.3rem;">
                    <button id="start-btn" ${controlStatus.start ? '' : 'disabled'} style="flex: 1; padding: 0.3rem; background: transparent; color: #4CAF50; border: 1px solid #4CAF50; border-radius: 4px; cursor: pointer; font-size: 0.8rem;">开始</button>
                    <button id="pause-btn" ${controlStatus.pause ? '' : 'disabled'} style="flex: 1; padding: 0.3rem; background: transparent; color: #FF9800; border: 1px solid #FF9800; border-radius: 4px; cursor: pointer; font-size: 0.8rem;">暂停</button>
                    <button id="stop-btn" ${controlStatus.stop ? '' : 'disabled'} style="flex: 1; padding: 0.3rem; background: transparent; color: #F44336; border: 1px solid #F44336; border-radius: 4px; cursor: pointer; font-size: 0.8rem;">停止</button>
                    <button id="scan-btn" ${controlStatus.scan ? '' : 'disabled'} style="flex: 1; padding: 0.3rem; background: transparent; color: #2196F3; border: 1px solid #2196F3; border-radius: 4px; cursor: pointer; font-size: 0.8rem;">🔍 扫描</button>
                </div>
            </div>
        `;
    }

    bindEvents() {
        this.container.querySelector('#start-btn')?.addEventListener('click', () => this.startMeeting());
        this.container.querySelector('#pause-btn')?.addEventListener('click', () => this.pauseMeeting());
        this.container.querySelector('#stop-btn')?.addEventListener('click', () => this.stopMeeting());
        this.container.querySelector('#scan-btn')?.addEventListener('click', () => this.handleScanningClick());
    }

    startMeeting() {
        console.log("开始会议");
        this.triggerEvent('control-meeting', { action: 'start' });
    }

    pauseMeeting() {
        console.log("暂停会议");
        this.triggerEvent('control-meeting', { action: 'pause' });
    }

    stopMeeting() {
        console.log("停止会议");
        this.triggerEvent('control-meeting', { action: 'stop' });
    }

    handleScanningClick() {
        console.log("处理扫描点击");
        this.triggerEvent('scan-documents');
    }

    /**
     * 公共方法：更新详细区内容
     * @param {string} content - 要显示的HTML内容
     */
    updateDetail(content) {
        const detailContent = this.container.querySelector('#detail-content');
        const detailPlaceholder = this.container.querySelector('#detail-placeholder');
        const detailTitle = this.container.querySelector('#detail-title');
        
        if (detailContent) {
            // 隐藏"详细"标题
            if (detailTitle) {
                detailTitle.style.display = 'none';
            }
            
            // 更新内容
            detailContent.innerHTML = content;
            if(detailPlaceholder) detailPlaceholder.style.display = 'none';
        }
    }
}