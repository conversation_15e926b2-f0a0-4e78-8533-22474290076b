/**
 * 项目风险评估 (RiskAssessmentComponent)
 * 
 * 功能：
 * - 显示项目的整体可靠性评分。
 * - 展示已识别并预防的风险。
 * - 提供文档健康报告的摘要。
 * - 提供查看完整报告的链接。
 * 
 * 数据依赖：
 * - risk_assessment: 包含可靠性评分和风险预防措施。
 * - health_report: 包含文档健康报告的数据。
 */
class RiskAssessmentComponent extends BaseComponent {
    getDataTypes() {
        return ['risk_assessment', 'health_report'];
    }

    render() {
        const riskData = this.getData('risk_assessment') || { reliability_score: 65, preventions: [] };
        const healthData = this.getData('health_report') || { reliability_score: 87, conflict_prevention_count: 3 };
        const reliabilityScore = riskData.reliability_score;
        const scoreClass = reliabilityScore < 70 ? 'danger' : (reliabilityScore < 90 ? 'warning' : 'success');
        const strokeDashoffset = 226 * (1 - reliabilityScore / 100);

        this.container.innerHTML = `
            <div class="area-title">项目风险评估 (Risk Assessment)</div>
            <div class="area-content">
                <!-- 可靠性评分仪表盘 -->
                <div class="circular-progress">
                    <svg>
                        <circle class="progress-circle progress-bg" cx="40" cy="40" r="36"></circle>
                        <circle class="progress-circle progress-bar-circle ${scoreClass}" cx="40" cy="40" r="36"
                                style="stroke-dashoffset: ${strokeDashoffset};" id="reliability-circle"></circle>
                    </svg>
                    <div class="progress-text">
                        <div class="progress-value" id="reliability-score">${reliabilityScore}%</div>
                        <div class="progress-label">可靠性</div>
                    </div>
                </div>

                <!-- V4.2 风险预防措施展示 -->
                <div class="risk-prevention-panel">
                    <div class="panel-title">
                        <span>🛡️</span>
                        <span>风险预防措施</span>
                    </div>
                    ${(riskData.preventions || []).map(p => this.renderPreventionItem(p)).join('')}
                </div>

                <!-- 文档健康报告 -->
                <div class="health-report-section">
                    <div class="report-title">📋 文档健康报告</div>
                    <div class="report-summary">
                        <div class="summary-item">
                            <span class="summary-label">可靠性评分:</span>
                            <span class="summary-value success">${healthData.reliability_score}%</span>
                        </div>
                        <div class="summary-item">
                            <span class="summary-label">预防冲突:</span>
                            <span class="summary-value info">${healthData.conflict_prevention_count}个</span>
                        </div>
                    </div>
                </div>

                <!-- 报告文件链接 -->
                <div style="margin-top: 1rem;">
                    <a href="#" id="report-link" class="output-link">📄 查看完整报告</a>
                </div>
            </div>
        `;
    }
    
    renderPreventionItem(prevention) {
        const riskBadgeClass = (prevention.level || 'critical').toLowerCase();
        const statusClass = (prevention.status || 'prevented').toLowerCase();
        return `
            <div class="prevention-item">
                <div class="risk-header">
                    <span class="risk-badge ${riskBadgeClass}">${prevention.level || 'CRITICAL'}</span>
                    <span class="prevention-status ${statusClass}">${prevention.status || '已预防'}</span>
                </div>
                <div class="risk-description">
                    ${prevention.description || '核心决策逻辑与全局护栏存在原则性冲突'}
                </div>
                <div class="prevention-detail">
                    <span class="prevention-label">预防机制:</span>
                    <span>${prevention.mechanism || 'ConstraintPreprocessor在阶段零已阻止此冲突进入知识库'}</span>
                </div>
            </div>
        `;
    }

    bindEvents() {
        const reportLink = this.container.querySelector('#report-link');
        if (reportLink) {
            reportLink.addEventListener('click', (e) => {
                e.preventDefault();
                // 在这里可以实现显示完整报告的逻辑，例如弹窗或跳转
                console.log('查看完整报告被点击');
                this.triggerEvent('show-full-report', { type: 'risk-assessment' });
            });
        }
    }
}