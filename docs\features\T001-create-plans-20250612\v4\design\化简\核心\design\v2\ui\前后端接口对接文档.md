# Project Manager V2 前后端接口对接文档

## 文档概述

本文档定义了Project Manager V2九宫格界面与后端V4.2治理引擎的完整接口规范，按照区域功能分门别类，确保前后端数据流的准确对接。

## 接口架构概览

### 通信方式
- **WebSocket**: 实时数据推送和状态同步
- **HTTP REST API**: 初始化数据获取和操作请求
- **数据格式**: JSON
- **认证方式**: Bearer Token

### 核心数据模型
基于V4.2设计文档的`AtomicConstraint`统一语义模型：

```typescript
interface AtomicConstraint {
    id: string;                    // 约束唯一标识
    category: 'boundary_condition' | 'constraint' | 'guardrail' | 'state_machine';
    type: string;                  // 约束类型
    params: Record<string, any>;   // 参数对象
    description: string;           // 约束描述
    source_block_id: string;       // 源文档块ID
    parent_id: string | null;      // 父约束ID (引用式分叉)
    created_at: string;            // 创建时间
    updated_at: string;            // 更新时间
}
```

---

## 区域1-2：项目进度监控 (Progress Monitoring)

### 组件列表
1. **四阶段流程进度条**
2. **阶段零专用指标面板**
3. **关键指标统计**
4. **整体进度展示**

### 所需接口

#### 1. 获取项目进度概览
```http
GET /api/v2/project/{project_id}/progress
```

**响应数据结构：**
```json
{
    "project_id": "string",
    "current_stage": {
        "stage_number": 0,
        "stage_name": "标准化与预验证",
        "status": "in_progress",
        "progress_percentage": 25
    },
    "stage_zero_metrics": {
        "pre_validation_pass_rate": 100,
        "conflict_prevention_count": 3,
        "schema_validation_passed": 25,
        "schema_validation_total": 25
    },
    "key_metrics": {
        "atomic_constraints_discovered": 25,
        "global_contracts_generated": 18,
        "documents_processed": 3,
        "documents_total": 5,
        "current_reliability_score": 87.7
    },
    "overall_progress": {
        "percentage": 25,
        "status": "阶段零进行中"
    }
}
```

#### 2. WebSocket实时进度更新
```javascript
// 监听事件类型
const PROGRESS_EVENTS = {
    STAGE_PROGRESS_UPDATE: 'stage_progress_update',
    METRIC_UPDATE: 'metric_update',
    RELIABILITY_SCORE_UPDATE: 'reliability_score_update'
};

// 事件数据格式
{
    "type": "stage_progress_update",
    "data": {
        "stage_number": 0,
        "progress_percentage": 30,
        "updated_metrics": {
            "atomic_constraints_discovered": 28
        }
    },
    "timestamp": "2025-01-31T14:17:30Z"
}
```

---

## 区域3：风险评估 (Risk Assessment)

### 组件列表
1. **圆形可靠性评分环**
2. **风险预防措施面板**
3. **文档健康报告**

### 所需接口

#### 1. 获取风险评估数据
```http
GET /api/v2/project/{project_id}/risk-assessment
```

**响应数据结构：**
```json
{
    "reliability_score": {
        "value": 87,
        "status": "good",
        "improvement_from_prevention": 22
    },
    "prevented_risks": [
        {
            "id": "risk_001",
            "level": "critical",
            "description": "核心决策逻辑与全局护栏存在原则性冲突",
            "prevention_mechanism": "ConstraintPreprocessor在阶段零已阻止此冲突进入知识库",
            "prevention_status": "prevented"
        },
        {
            "id": "risk_002",
            "level": "high",
            "description": "UserService 未定义明确的响应时间边界条件",
            "prevention_mechanism": "引用式分叉机制自动继承GB001的边界条件",
            "prevention_status": "prevented"
        }
    ],
    "document_health_report": {
        "reliability_score": 87,
        "prevented_conflicts": 3,
        "report_url": "/api/v2/project/{project_id}/health-report"
    }
}
```

#### 2. WebSocket风险状态更新
```javascript
{
    "type": "risk_assessment_update",
    "data": {
        "reliability_score": 89,
        "new_prevented_risk": {
            "id": "risk_003",
            "level": "medium",
            "description": "新发现的潜在冲突已被预防"
        }
    },
    "timestamp": "2025-01-31T14:17:30Z"
}
```

---

## 区域4：项目经理状态 (Project Manager Status)

### 组件列表
1. **AI角色状态指示器**
2. **当前任务显示**
3. **任务进度条**
4. **处理时间统计**

### 所需接口

#### 1. 获取项目经理状态
```http
GET /api/v2/project/{project_id}/manager-status
```

**响应数据结构：**
```json
{
    "current_manager": {
        "role": "首席架构师AI",
        "status": "active",
        "status_indicator": "status-active"
    },
    "current_task": {
        "description": "从01号文档中识别设计意图并进行实体分类",
        "status": "in_progress",
        "progress_percentage": 75,
        "status_indicator": "status-thinking"
    },
    "work_status": {
        "status": "ACTIVE",
        "color": "#4CAF50"
    },
    "current_document": {
        "name": "1-总体架构设计-V2.md",
        "status": "processing",
        "status_indicator": "status-converging"
    },
    "processing_time": {
        "elapsed_seconds": 150,
        "formatted": "2分30秒",
        "color": "#FF9800"
    }
}
```

#### 2. WebSocket状态实时更新
```javascript
{
    "type": "manager_status_update",
    "data": {
        "current_task": {
            "description": "正在生成全局契约点",
            "progress_percentage": 85
        },
        "processing_time": {
            "elapsed_seconds": 180,
            "formatted": "3分00秒"
        }
    },
    "timestamp": "2025-01-31T14:17:30Z"
}
```

---

## 区域5：Python主持人算法思维 (Algorithm Thinking)

### 组件列表
1. **AI-算法协同展示**
2. **算法思维日志**
3. **可展开的详情箭头**

### 所需接口

#### 1. 获取算法思维日志
```http
GET /api/v2/project/{project_id}/algorithm-logs
```

**响应数据结构：**
```json
{
    "ai_algorithm_collaboration": {
        "ai_role": "首席架构师AI",
        "algorithm_component": "ConstraintPreprocessor",
        "current_status": "pre_validation_passed",
        "result": "已生成原子约束 GB001"
    },
    "process_logs": [
        {
            "id": "startup_check_ide",
            "timestamp": "14:17:30",
            "message": "启动检查: 正在验证IDE AI连接状态...✅ 连接正常",
            "expandable": true,
            "ai_comm_details": {
                "request": "IDE AI连接状态检查",
                "response_time_ms": 23,
                "status": "连接正常",
                "tools_count": 6
            },
            "py_ops_details": {
                "operation": "验证IDE连接",
                "execution_time_ms": 12,
                "memory_usage_mb": 2.3,
                "cpu_usage_percent": 0.1
            }
        }
    ]
}
```

#### 2. WebSocket日志实时推送
```javascript
{
    "type": "algorithm_log_entry",
    "data": {
        "id": "document_analysis_start",
        "timestamp": "14:17:39",
        "message": "开始执行文档分析...",
        "expandable": true
    },
    "timestamp": "2025-01-31T14:17:39Z"
}
```

---

## 区域6：约束审查 (Constraint Review)

### 组件列表
1. **AtomicConstraint详细结构展示**
2. **约束字段网格**
3. **参数树形结构**
4. **血统关系展示**

### 所需接口

#### 1. 获取约束详细信息
```http
GET /api/v2/project/{project_id}/constraints/{constraint_id}
```

**响应数据结构：**
```json
{
    "id": "GB001",
    "category": "boundary_condition",
    "type": "response_time",
    "params": {
        "target_entity": "api_request",
        "max_ms": 1000,
        "unit": "milliseconds",
        "on_exceed_action": "return_http_429"
    },
    "description": "所有面向用户的API必须在1000ms内响应",
    "source_block_id": "block_001",
    "parent_id": null,
    "created_at": "2025-01-31T14:17:30Z",
    "updated_at": "2025-01-31T14:17:30Z",
    "lineage": {
        "children_count": 2,
        "children_ids": ["LB002", "BC003"]
    }
}
```

---

## 区域7：知识库可视化 (Knowledge Base)

### 组件列表
1. **约束节点图谱**
2. **引用式分叉可视化**
3. **连接线关系展示**
4. **帮助弹窗**

### 所需接口

#### 1. 获取知识库图谱数据
```http
GET /api/v2/project/{project_id}/knowledge-graph
```

**响应数据结构：**
```json
{
    "nodes": [
        {
            "id": "GB001",
            "category": "boundary_condition",
            "type": "response_time",
            "position": { "x": 30, "y": 30 },
            "parent_id": null,
            "is_forked": false,
            "description": "GB001: 全局API响应时间边界条件"
        },
        {
            "id": "LB002",
            "category": "constraint",
            "type": "user_service",
            "position": { "x": 120, "y": 80 },
            "parent_id": "GB001",
            "is_forked": true,
            "description": "LB002: 用户服务约束(引用式分叉)"
        }
    ],
    "connections": [
        {
            "from": "GB001",
            "to": "LB002",
            "type": "fork",
            "style": "dashed"
        }
    ]
}
```

---

## 区域8：人类输入控制区 (Human Input Control)

### 组件列表
1. **详细区内容展示**
2. **项目选择器**
3. **控制按钮组**

### 所需接口

#### 1. 获取项目列表
```http
GET /api/v2/projects
```

#### 2. 控制操作接口
```http
POST /api/v2/project/{project_id}/actions/start
POST /api/v2/project/{project_id}/actions/pause
POST /api/v2/project/{project_id}/actions/stop
POST /api/v2/project/{project_id}/actions/scan
```

---

## 区域9：项目交付结果 (Project Deliverables)

### 组件列表
1. **输出文件列表**
2. **处理统计进度条**
3. **下载链接**

### 所需接口

#### 1. 获取交付结果
```http
GET /api/v2/project/{project_id}/deliverables
```

**响应数据结构：**
```json
{
    "output_files": [
        {
            "name": "约束规范文档.md",
            "type": "constraint_spec",
            "size": "2.3MB",
            "status": "completed",
            "download_url": "/api/v2/files/{file_id}/download"
        }
    ],
    "processing_statistics": {
        "documents_processed": { "current": 3, "total": 5, "percentage": 60 },
        "constraints_generated": { "count": 25, "percentage": 83 },
        "risks_identified": { "count": 2, "percentage": 40 },
        "processing_time": { "seconds": 150, "formatted": "2分30秒", "percentage": 75 }
    }
}
```

---

## WebSocket事件汇总

### 事件类型定义
```javascript
const WEBSOCKET_EVENTS = {
    // 进度相关
    STAGE_PROGRESS_UPDATE: 'stage_progress_update',
    METRIC_UPDATE: 'metric_update',
    
    // 风险相关
    RISK_ASSESSMENT_UPDATE: 'risk_assessment_update',
    RELIABILITY_SCORE_UPDATE: 'reliability_score_update',
    
    // 状态相关
    MANAGER_STATUS_UPDATE: 'manager_status_update',
    ALGORITHM_LOG_ENTRY: 'algorithm_log_entry',
    
    // 约束相关
    CONSTRAINT_CREATED: 'constraint_created',
    CONSTRAINT_UPDATED: 'constraint_updated',
    
    // 知识库相关
    KNOWLEDGE_GRAPH_UPDATE: 'knowledge_graph_update',
    
    // 交付相关
    DELIVERABLE_READY: 'deliverable_ready',
    PROCESSING_COMPLETE: 'processing_complete'
};
```

### 连接管理
```javascript
// WebSocket连接URL
const WS_URL = `ws://localhost:25526/ws/project/{project_id}`;

// 认证头
const WS_HEADERS = {
    'Authorization': 'Bearer {token}'
};
```

---

## 错误处理规范

### HTTP错误码
- `400` - 请求参数错误
- `401` - 认证失败
- `403` - 权限不足
- `404` - 资源不存在
- `500` - 服务器内部错误

### WebSocket错误事件
```javascript
{
    "type": "error",
    "data": {
        "code": "CONSTRAINT_VALIDATION_FAILED",
        "message": "约束验证失败",
        "details": "Schema验证不通过"
    },
    "timestamp": "2025-01-31T14:17:30Z"
}
```

---

## 性能要求

### 响应时间
- HTTP API: < 200ms
- WebSocket推送: < 50ms
- 文件下载: 支持断点续传

### 数据量限制
- 单次API响应: < 1MB
- WebSocket消息: < 100KB
- 文件上传: < 100MB

---

## 安全要求

### 数据验证
- 所有输入参数必须验证
- SQL注入防护
- XSS攻击防护

### 访问控制
- JWT Token认证
- 项目级权限控制
- 操作日志记录
