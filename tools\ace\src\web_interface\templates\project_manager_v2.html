<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>V2项目经理工作台 - 虚拟项目经理交互版</title>
    
    <!-- Socket.IO -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.js"></script>

    <!-- 复用nine_grid的基础样式和脚本 -->
    <link rel="stylesheet" href="/static/css/nine_grid_base.css">
    <script src="/static/js/nine_grid_base.js"></script>
    
    <style>
        /* V2特有样式 - 复用nine_grid的基础样式 */
        
        /* V2特有样式 - 复用nine_grid的基础样式 */
        
        /* V2知识库可视化样式 */
        .knowledge-graph {
            width: 100%;
            height: 200px;
            background: #2D2D30;
            border-radius: 4px;
            position: relative;
            overflow: hidden;
        }

        .constraint-node {
            position: absolute;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.6rem;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid;
        }

        .constraint-node.global {
            background: #4CAF50;
            border-color: #4CAF50;
            color: white;
        }

        .constraint-node.local {
            background: #2196F3;
            border-color: #2196F3;
            color: white;
        }

        .constraint-node.boundary {
            background: #FF9800;
            border-color: #FF9800;
            color: white;
        }

        .constraint-node.state-machine {
            background: #9C27B0;
            border-color: #9C27B0;
            color: white;
        }

        .constraint-node:hover {
            transform: scale(1.1);
            z-index: 10;
        }

        .constraint-connection {
            position: absolute;
            height: 2px;
            background: #666;
            transform-origin: left center;
        }

        .constraint-connection.fork {
            background: #FF9800;
            height: 3px;
        }

        /* V2风险评估仪表盘 */
        .risk-dashboard {
            background: #2D2D30;
            padding: 0.8rem;
            border-radius: 4px;
            margin-bottom: 0.8rem;
        }

        .risk-score {
            text-align: center;
            margin-bottom: 0.5rem;
        }

        .risk-score-value {
            font-size: 1.5rem;
            font-weight: bold;
        }

        .risk-score.critical .risk-score-value {
            color: #F44336;
        }

        .risk-score.high .risk-score-value {
            color: #FF9800;
        }

        .risk-score.medium .risk-score-value {
            color: #FFC107;
        }

        .risk-score.low .risk-score-value {
            color: #4CAF50;
        }

        .risk-score-label {
            font-size: 0.8rem;
            color: #BBBBBB;
            margin-top: 0.2rem;
        }

        /* V2四阶段流程进度 */
        .stage-progress {
            margin-bottom: 1rem;
        }

        .stage-item {
            display: flex;
            align-items: center;
            margin-bottom: 0.5rem;
            padding: 0.3rem;
            border-radius: 3px;
            transition: background-color 0.2s ease;
        }

        .stage-item.current {
            background: rgba(0, 120, 212, 0.1);
            border-left: 3px solid #0078D4;
        }

        .stage-item.completed {
            background: rgba(76, 175, 80, 0.1);
            border-left: 3px solid #4CAF50;
        }

        .stage-item.pending {
            background: rgba(158, 158, 158, 0.1);
            border-left: 3px solid #9E9E9E;
        }

        .stage-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 0.5rem;
            flex-shrink: 0;
        }

        .stage-indicator.current {
            background: #0078D4;
            animation: pulse 2s infinite;
        }

        .stage-indicator.completed {
            background: #4CAF50;
        }

        .stage-indicator.pending {
            background: #9E9E9E;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        /* V2决策中心卡片 */
        .decision-card {
            background: #2D2D30;
            padding: 0.8rem;
            border-radius: 4px;
            margin-bottom: 0.5rem;
            border-left: 4px solid;
        }

        .decision-card.insight {
            border-left-color: #2196F3;
        }

        .decision-card.action {
            border-left-color: #FF9800;
        }

        .decision-card.log {
            border-left-color: #9E9E9E;
        }

        .decision-card-title {
            font-weight: bold;
            margin-bottom: 0.5rem;
            color: #0078D4;
        }

        .decision-options {
            display: flex;
            gap: 0.5rem;
            margin-top: 0.5rem;
        }

        .decision-btn {
            padding: 0.4rem 0.8rem;
            background: transparent;
            border: 1px solid #0078D4;
            color: #0078D4;
            border-radius: 3px;
            cursor: pointer;
            font-size: 0.8rem;
            transition: all 0.2s ease;
        }

        .decision-btn:hover {
            background: #0078D4;
            color: white;
        }

        .decision-btn.approve {
            border-color: #4CAF50;
            color: #4CAF50;
        }

        .decision-btn.approve:hover {
            background: #4CAF50;
            color: white;
        }

        .decision-btn.reject {
            border-color: #F44336;
            color: #F44336;
        }

        .decision-btn.reject:hover {
            background: #F44336;
            color: white;
        }

        /* V2项目输出样式 */
        .output-status {
            text-align: center;
            margin-bottom: 0.8rem;
        }

        .output-badge {
            display: inline-block;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-weight: bold;
            font-size: 0.9rem;
        }

        .output-badge.success {
            background: #4CAF50;
            color: white;
        }

        .output-badge.processing {
            background: #FF9800;
            color: white;
            animation: processingPulse 1s infinite;
        }

        .output-badge.error {
            background: #F44336;
            color: white;
        }

        .output-links {
            margin-top: 0.8rem;
        }

        .output-link {
            display: block;
            padding: 0.5rem;
            background: #393B40;
            color: #0078D4;
            text-decoration: none;
            border-radius: 3px;
            margin-bottom: 0.3rem;
            transition: background-color 0.2s ease;
        }

        .output-link:hover {
            background: #0078D4;
            color: white;
        }

        @keyframes processingPulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        /* V2约束详情样式 */
        .constraint-detail {
            background: #2D2D30;
            padding: 0.8rem;
            border-radius: 4px;
            margin-bottom: 0.5rem;
        }

        .constraint-detail-header {
            font-weight: bold;
            color: #0078D4;
            margin-bottom: 0.5rem;
            border-bottom: 1px solid #3C3F41;
            padding-bottom: 0.3rem;
        }

        .constraint-detail-item {
            margin-bottom: 0.3rem;
            font-size: 0.9rem;
        }

        .constraint-detail-item strong {
            color: #BBBBBB;
        }

        .constraint-params {
            background: #1E1F22;
            padding: 0.5rem;
            border-radius: 3px;
            font-family: monospace;
            font-size: 0.7rem;
            margin-top: 0.3rem;
        }



        /* 项目选择器样式 */
        .project-selector {
            margin-bottom: 0.5rem;
            padding: 0.3rem;
            background: #2A2D30;
            border: 1px solid #3C3F41;
            border-radius: 4px;
        }

        .project-selector select {
            flex: 1;
            background: #2A2D30;
            color: #BBBBBB;
            border: 1px solid #3C3F41;
            border-radius: 3px;
            padding: 0.2rem;
            font-size: 0.8rem;
        }

        .project-selector button {
            padding: 0.2rem 0.4rem;
            background: transparent;
            border-radius: 3px;
            cursor: pointer;
            font-size: 0.7rem;
            transition: opacity 0.2s;
        }

        .project-selector button:hover {
            opacity: 1;
        }





        /* V2知识库可视化样式 */
        .knowledge-graph {
            width: 100%;
            height: 200px;
            background: linear-gradient(135deg, #2D2D30, #1E1F22);
            border-radius: 4px;
            position: relative;
            overflow: hidden;
            border: 1px solid #3C3F41;
        }

        .constraint-node {
            position: absolute;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.6rem;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
        }

        .constraint-node:hover {
            transform: scale(1.2);
            z-index: 10;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.5);
        }

        .constraint-node.global {
            background: linear-gradient(135deg, #4CAF50, #66BB6A);
            border-color: #4CAF50;
            color: white;
        }

        .constraint-node.global:hover {
            box-shadow: 0 4px 16px rgba(76, 175, 80, 0.6);
        }

        .constraint-node.local {
            background: linear-gradient(135deg, #2196F3, #42A5F5);
            border-color: #2196F3;
            color: white;
        }

        .constraint-node.local:hover {
            box-shadow: 0 4px 16px rgba(33, 150, 243, 0.6);
        }

        .constraint-node.boundary {
            background: linear-gradient(135deg, #FF9800, #FFA726);
            border-color: #FF9800;
            color: white;
        }

        .constraint-node.boundary:hover {
            box-shadow: 0 4px 16px rgba(255, 152, 0, 0.6);
        }

        .constraint-node.state-machine {
            background: linear-gradient(135deg, #9C27B0, #AB47BC);
            border-color: #9C27B0;
            color: white;
        }

        .constraint-node.state-machine:hover {
            box-shadow: 0 4px 16px rgba(156, 39, 176, 0.6);
        }

        /* 约束连接线 */
        .constraint-connection {
            position: absolute;
            height: 2px;
            background: linear-gradient(90deg, rgba(187, 187, 187, 0.3), rgba(187, 187, 187, 0.1));
            transform-origin: left center;
            pointer-events: none;
            z-index: 1;
        }

        .constraint-connection.active {
            background: linear-gradient(90deg, rgba(0, 120, 212, 0.6), rgba(0, 120, 212, 0.2));
            height: 3px;
        }

        /* 节点标签 */
        .node-label {
            position: absolute;
            bottom: -20px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 0.5rem;
            color: #888;
            white-space: nowrap;
            pointer-events: none;
        }

        /* 节点详情提示 */
        .node-tooltip {
            position: absolute;
            background: rgba(42, 45, 48, 0.95);
            border: 1px solid #3C3F41;
            border-radius: 4px;
            padding: 0.5rem;
            font-size: 0.7rem;
            color: #BBBBBB;
            z-index: 20;
            pointer-events: none;
            opacity: 0;
            transition: opacity 0.3s ease;
            max-width: 200px;
        }

        .node-tooltip.show {
            opacity: 1;
        }

        /* V2决策中心卡片样式 */
        .decision-card {
            background: linear-gradient(135deg, #2A2D30, #1E1F22);
            border: 1px solid #3C3F41;
            border-radius: 6px;
            padding: 0.8rem;
            margin-bottom: 0.8rem;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .decision-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, transparent, rgba(0, 120, 212, 0.6), transparent);
        }

        .decision-card.insight::before {
            background: linear-gradient(90deg, transparent, rgba(76, 175, 80, 0.6), transparent);
        }

        .decision-card.action::before {
            background: linear-gradient(90deg, transparent, rgba(255, 152, 0, 0.8), transparent);
            animation: pulse-warning 2s infinite;
        }

        .decision-card.log::before {
            background: linear-gradient(90deg, transparent, rgba(33, 150, 243, 0.6), transparent);
        }

        @keyframes pulse-warning {
            0%, 100% { opacity: 0.8; }
            50% { opacity: 1; }
        }

        .decision-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            border-color: #0078D4;
        }

        .decision-card-title {
            font-weight: bold;
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .decision-card.insight .decision-card-title {
            color: #4CAF50;
        }

        .decision-card.action .decision-card-title {
            color: #FF9800;
        }

        .decision-card.log .decision-card-title {
            color: #2196F3;
        }

        .decision-options {
            display: flex;
            gap: 0.5rem;
            margin-top: 0.8rem;
        }

        .decision-btn {
            flex: 1;
            padding: 0.5rem;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s ease;
        }

        .decision-btn.approve {
            background: linear-gradient(135deg, #4CAF50, #66BB6A);
            color: white;
        }

        .decision-btn.approve:hover {
            background: linear-gradient(135deg, #66BB6A, #4CAF50);
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(76, 175, 80, 0.4);
        }

        .decision-btn.reject {
            background: linear-gradient(135deg, #F44336, #EF5350);
            color: white;
        }

        .decision-btn.reject:hover {
            background: linear-gradient(135deg, #EF5350, #F44336);
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(244, 67, 54, 0.4);
        }

        /* 决策日志样式 */
        #decision-log {
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 0.75rem;
            max-height: 150px;
            overflow-y: auto;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 4px;
            padding: 0.5rem;
        }

        #decision-log div {
            margin-bottom: 0.2rem;
            padding: 0.1rem 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.05);
        }

        #decision-log div:last-child {
            border-bottom: none;
        }



        /* V2响应式布局和字体层次优化 */
        .area-title {
            font-size: calc(0.5rem + 0.15vw);
            font-weight: bold;
            color: #0078D4;
            margin: 0 0 0.3rem 0;
            border-bottom: 1px solid #3C3F41;
            padding-bottom: 0.2rem;
        }

        .area-content {
            font-size: clamp(0.75rem, 1vw, 0.9rem);
            line-height: 1.5;
            padding: 0.2rem;
            position: relative; /* 为弹窗定位提供基准 */
        }

        /* 统一间距系统 */
        .status-item {
            margin-bottom: 0.6rem;
            padding: 0.3rem 0;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .stage-item {
            margin-bottom: 0.5rem;
            padding: 0.4rem 0;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.85rem;
        }

        /* 改进的卡片间距 */
        .decision-card {
            margin-bottom: 1rem;
            padding: 1rem;
        }

        .decision-card:last-child {
            margin-bottom: 0;
        }

        /* 响应式字体大小 */
        @media (max-width: 1200px) {
            .area-title {
                font-size: 0.9rem;
            }

            .area-content {
                font-size: 0.8rem;
            }

            .constraint-node {
                width: 40px;
                height: 40px;
                font-size: 0.5rem;
            }

            .circular-progress {
                width: 60px;
                height: 60px;
            }
        }

        @media (max-width: 900px) {
            .nine-grid-container {
                grid-gap: 1px;
            }

            .grid-area {
                padding: 0.5rem;
            }

            .area-title {
                font-size: 0.8rem;
                margin-bottom: 0.5rem;
            }

            .area-content {
                font-size: 0.75rem;
            }
        }

        /* 改进的滚动条样式 */
        .vscode-scrollbar::-webkit-scrollbar {
            width: 8px;
        }

        .vscode-scrollbar::-webkit-scrollbar-track {
            background: #1E1F22;
            border-radius: 4px;
        }

        .vscode-scrollbar::-webkit-scrollbar-thumb {
            background: linear-gradient(180deg, #424242, #2A2D30);
            border-radius: 4px;
            border: 1px solid #1E1F22;
        }

        .vscode-scrollbar::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(180deg, #4F4F4F, #3C3F41);
        }

        /* 统一的输入框样式 */
        input, textarea, select {
            background: #2A2D30 !important;
            color: #BBBBBB !important;
            border: 1px solid #3C3F41 !important;
            border-radius: 4px !important;
            padding: 0.5rem !important;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
            transition: border-color 0.3s ease !important;
        }

        input:focus, textarea:focus, select:focus {
            outline: none !important;
            border-color: #0078D4 !important;
            box-shadow: 0 0 0 2px rgba(0, 120, 212, 0.2) !important;
        }

        /* 改进的按钮样式 */
        button {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            font-weight: 500;
            letter-spacing: 0.3px;
            transition: all 0.3s ease;
        }

        /* 数据展示的字体层次 */
        .metric-value {
            font-weight: 700;
            font-size: 1.1em;
        }

        .metric-label {
            font-weight: 400;
            opacity: 0.9;
        }

        .secondary-text {
            font-size: 0.85em;
            opacity: 0.7;
        }

        /* V4.2阶段零突出显示样式 */
        .stage-zero-highlight {
            background: linear-gradient(135deg, rgba(76, 175, 80, 0.1), rgba(76, 175, 80, 0.05));
            border: 1px solid rgba(76, 175, 80, 0.3);
            border-radius: 4px;
            padding: 0.5rem;
            position: relative;
        }

        .badge {
            display: inline-block;
            padding: 0.1rem 0.4rem;
            border-radius: 3px;
            font-size: 0.6rem;
            font-weight: bold;
            margin-left: 0.5rem;
        }

        .badge-reliability {
            background: linear-gradient(135deg, #4CAF50, #66BB6A);
            color: white;
            box-shadow: 0 2px 4px rgba(76, 175, 80, 0.3);
        }

        .stage-zero-metrics {
            margin-top: 1rem;
            padding: 0.8rem;
            background: linear-gradient(135deg, rgba(0, 120, 212, 0.05), rgba(0, 120, 212, 0.02));
            border: 1px solid rgba(0, 120, 212, 0.2);
            border-radius: 4px;
        }

        .metrics-title {
            font-weight: bold;
            margin-bottom: 0.8rem;
            color: #0078D4;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .metric-item {
            margin-bottom: 0.6rem;
        }

        .metric-label {
            font-size: 0.85rem;
            color: #BBBBBB;
        }

        .metric-value {
            font-weight: bold;
            font-size: 0.9rem;
        }

        .metric-value.success {
            color: #4CAF50;
        }

        .metric-value.info {
            color: #2196F3;
        }

        .metric-value.warning {
            color: #FF9800;
        }

        /* V4.2 AtomicConstraint详细展示样式 */
        .constraint-detail-enhanced {
            padding: 1rem;
            background: linear-gradient(135deg, #2A2D30, #1E1F22);
            border: 1px solid #3C3F41;
            border-radius: 6px;
            font-family: 'Consolas', 'Monaco', monospace;
        }

        .constraint-header {
            display: flex;
            align-items: center;
            gap: 0.8rem;
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid #3C3F41;
        }

        .constraint-id {
            font-size: 1.2rem;
            font-weight: bold;
            color: #0078D4;
            background: rgba(0, 120, 212, 0.1);
            padding: 0.3rem 0.6rem;
            border-radius: 4px;
        }

        .constraint-category {
            padding: 0.2rem 0.5rem;
            border-radius: 3px;
            font-size: 0.7rem;
            font-weight: bold;
            text-transform: uppercase;
        }

        .category-boundary-condition {
            background: #FF9800;
            color: white;
        }

        .category-constraint {
            background: #4CAF50;
            color: white;
        }

        .category-guardrail {
            background: #F44336;
            color: white;
        }

        .category-state-machine {
            background: #9C27B0;
            color: white;
        }

        .constraint-section {
            margin-bottom: 1rem;
        }

        .section-title {
            font-weight: bold;
            color: #0078D4;
            margin-bottom: 0.5rem;
            font-size: 0.9rem;
        }

        .field-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 0.5rem;
        }

        .field-item {
            display: flex;
            align-items: center;
            gap: 0.3rem;
            padding: 0.3rem;
            background: rgba(255, 255, 255, 0.02);
            border-radius: 3px;
        }

        .field-label {
            color: #888;
            font-size: 0.8rem;
            min-width: 60px;
        }

        .field-value {
            color: #BBBBBB;
            font-weight: bold;
            font-size: 0.8rem;
        }

        .params-tree {
            background: rgba(0, 0, 0, 0.2);
            border-radius: 4px;
            padding: 0.5rem;
        }

        .param-node {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.2rem 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.05);
        }

        .param-node:last-child {
            border-bottom: none;
        }

        .param-key {
            color: #4FC3F7;
            font-size: 0.8rem;
        }

        .param-value {
            color: #81C784;
            font-size: 0.8rem;
            font-weight: bold;
        }

        .lineage-info {
            background: rgba(0, 120, 212, 0.05);
            border-radius: 4px;
            padding: 0.5rem;
        }

        .lineage-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.2rem 0;
        }

        .lineage-label {
            color: #0078D4;
            font-size: 0.8rem;
        }

        .lineage-value {
            color: #BBBBBB;
            font-size: 0.8rem;
            font-weight: bold;
        }

        /* V4.2 引用式分叉机制可视化样式 */
        .constraint-node.forked {
            border: 2px dashed #FF9800;
            position: relative;
            background: linear-gradient(135deg, rgba(255, 152, 0, 0.1), rgba(255, 152, 0, 0.05));
        }

        .constraint-node.forked:hover {
            border-color: #FFB74D;
            box-shadow: 0 4px 16px rgba(255, 152, 0, 0.6);
        }

        .fork-indicator {
            position: absolute;
            bottom: -18px;
            right: -8px;
            font-size: 0.5rem;
            color: #FF9800;
            background: rgba(255, 152, 0, 0.1);
            padding: 0.1rem 0.2rem;
            border-radius: 2px;
            border: 1px solid #FF9800;
            font-weight: bold;
        }

        .constraint-connection.reference {
            background: linear-gradient(90deg, rgba(33, 150, 243, 0.6), rgba(33, 150, 243, 0.2));
            height: 3px;
            border-radius: 2px;
        }

        .constraint-connection.fork {
            background: linear-gradient(90deg, rgba(255, 152, 0, 0.8), rgba(255, 152, 0, 0.3));
            height: 2px;
            border-style: dashed;
            border-top: 2px dashed #FF9800;
            background: none;
        }

        /* 约束类别特定样式 */
        .constraint-node[data-category="boundary_condition"] {
            border-color: #FF9800;
        }

        .constraint-node[data-category="constraint"] {
            border-color: #4CAF50;
        }

        .constraint-node[data-category="guardrail"] {
            border-color: #F44336;
        }

        .constraint-node[data-category="state_machine"] {
            border-color: #9C27B0;
        }

        /* V4.2 AI-算法协同展示样式 */
        .ai-algorithm-collaboration {
            background: linear-gradient(135deg, rgba(0, 120, 212, 0.1), rgba(0, 120, 212, 0.05));
            border: 1px solid rgba(0, 120, 212, 0.3);
            border-radius: 6px;
            padding: 1rem;
            margin-bottom: 1rem;
        }

        .collaboration-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid rgba(0, 120, 212, 0.2);
        }

        .ai-role, .algorithm-role {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.3rem 0.6rem;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: bold;
        }

        .ai-role {
            background: linear-gradient(135deg, rgba(76, 175, 80, 0.2), rgba(76, 175, 80, 0.1));
            color: #4CAF50;
        }

        .algorithm-role {
            background: linear-gradient(135deg, rgba(255, 152, 0, 0.2), rgba(255, 152, 0, 0.1));
            color: #FF9800;
        }

        .collaboration-arrow {
            font-size: 1.2rem;
            color: #0078D4;
            font-weight: bold;
        }

        .collaboration-process {
            display: flex;
            flex-direction: column;
            gap: 0.8rem;
        }

        .ai-step, .algorithm-step, .result-step {
            padding: 0.6rem;
            border-radius: 4px;
            border-left: 3px solid;
        }

        .ai-step {
            background: rgba(76, 175, 80, 0.05);
            border-left-color: #4CAF50;
        }

        .algorithm-step {
            background: rgba(255, 152, 0, 0.05);
            border-left-color: #FF9800;
        }

        .result-step {
            background: rgba(33, 150, 243, 0.05);
            border-left-color: #2196F3;
        }

        .step-header {
            font-weight: bold;
            font-size: 0.8rem;
            margin-bottom: 0.3rem;
            color: #0078D4;
        }

        .step-content {
            font-size: 0.8rem;
            color: #BBBBBB;
        }

        .validation-checks {
            display: flex;
            flex-direction: column;
            gap: 0.2rem;
        }

        .check-item {
            display: flex;
            align-items: center;
            gap: 0.3rem;
            font-size: 0.75rem;
        }

        .check-item.success {
            color: #4CAF50;
        }

        .check-icon {
            font-weight: bold;
        }

        .result-badge {
            display: inline-block;
            padding: 0.2rem 0.4rem;
            border-radius: 3px;
            font-size: 0.7rem;
            font-weight: bold;
            margin-right: 0.5rem;
        }

        .result-badge.success {
            background: #4CAF50;
            color: white;
        }

        .result-detail {
            font-size: 0.8rem;
            color: #BBBBBB;
            margin-top: 0.2rem;
        }

        /* 紧凑版AI-算法协同样式 (用于区域5) */
        .ai-algorithm-collaboration-compact {
            background: linear-gradient(135deg, rgba(0, 120, 212, 0.08), rgba(0, 120, 212, 0.03));
            border: 1px solid rgba(0, 120, 212, 0.2);
            border-radius: 4px;
            padding: 0.6rem;
            margin-bottom: 0.8rem;
        }

        .collaboration-header-compact {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 0.5rem;
        }

        .ai-role-compact, .algorithm-role-compact {
            display: flex;
            align-items: center;
            gap: 0.3rem;
            font-size: 0.7rem;
            font-weight: bold;
        }

        .ai-role-compact {
            color: #4CAF50;
        }

        .algorithm-role-compact {
            color: #FF9800;
        }

        .validation-status-compact {
            font-size: 0.7rem;
        }

        /* V4.2 风险预防措施样式 */
        .risk-prevention-panel {
            margin-bottom: 1rem;
            padding: 0.8rem;
            background: linear-gradient(135deg, rgba(76, 175, 80, 0.05), rgba(76, 175, 80, 0.02));
            border: 1px solid rgba(76, 175, 80, 0.2);
            border-radius: 4px;
        }

        .panel-title {
            font-weight: bold;
            margin-bottom: 0.8rem;
            color: #4CAF50;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.9rem;
        }

        .prevention-item {
            margin-bottom: 0.8rem;
            padding: 0.6rem;
            background: rgba(255, 255, 255, 0.02);
            border-radius: 4px;
            border-left: 3px solid #4CAF50;
        }

        .prevention-item:last-child {
            margin-bottom: 0;
        }

        .risk-header {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 0.4rem;
        }

        .prevention-status {
            padding: 0.1rem 0.4rem;
            border-radius: 3px;
            font-size: 0.6rem;
            font-weight: bold;
        }

        .prevention-status.prevented {
            background: #4CAF50;
            color: white;
        }

        .prevention-status.monitoring {
            background: #FF9800;
            color: white;
        }

        .risk-description {
            font-size: 0.8rem;
            color: #BBBBBB;
            margin-bottom: 0.4rem;
            line-height: 1.3;
        }

        .prevention-detail {
            font-size: 0.75rem;
            color: #888;
            line-height: 1.3;
        }

        .prevention-label {
            color: #4CAF50;
            font-weight: bold;
        }

        /* 文档健康报告样式 */
        .health-report-section {
            padding: 0.8rem;
            background: linear-gradient(135deg, rgba(33, 150, 243, 0.05), rgba(33, 150, 243, 0.02));
            border: 1px solid rgba(33, 150, 243, 0.2);
            border-radius: 4px;
        }

        .report-title {
            font-weight: bold;
            color: #2196F3;
            margin-bottom: 0.6rem;
            font-size: 0.9rem;
        }

        .report-summary {
            display: flex;
            flex-direction: column;
            gap: 0.3rem;
        }

        .summary-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .summary-label {
            font-size: 0.8rem;
            color: #BBBBBB;
        }

        .summary-value {
            font-weight: bold;
            font-size: 0.8rem;
        }

        .summary-value.success {
            color: #4CAF50;
        }

        .summary-value.info {
            color: #2196F3;
        }

        .summary-value.warning {
            color: #FF9800;
        }

        /* 知识库帮助图标样式 */
        .help-icon {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 18px;
            height: 18px;
            margin-left: 0.5rem;
            cursor: pointer;
            border-radius: 50%;
            background: linear-gradient(135deg, #0078D4, #40A9FF);
            transition: all 0.3s ease;
            position: relative;
        }

        .help-icon:hover {
            background: linear-gradient(135deg, #40A9FF, #0078D4);
            transform: scale(1.1);
            box-shadow: 0 2px 8px rgba(0, 120, 212, 0.4);
        }

        .help-symbol {
            color: white;
            font-size: 0.7rem;
            font-weight: bold;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        /* 知识库帮助弹窗样式 */
        .knowledge-help-popup {
            position: absolute;
            background: linear-gradient(135deg, #2A2D30, #1E1F22);
            border: 2px solid #0078D4;
            border-radius: 8px;
            padding: 1rem;
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.5);
            z-index: 1000;
            min-width: 300px;
            max-width: 400px;
            font-size: 0.8rem;
            line-height: 1.4;
            opacity: 0;
            transform: translateY(-10px);
            transition: all 0.3s ease;
            pointer-events: none;
        }

        .knowledge-help-popup.show {
            opacity: 1;
            transform: translateY(0);
            pointer-events: auto;
        }

        .help-popup-title {
            font-weight: bold;
            color: #0078D4;
            margin-bottom: 0.8rem;
            font-size: 0.9rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .help-popup-section {
            margin-bottom: 0.8rem;
        }

        .help-popup-section:last-child {
            margin-bottom: 0;
        }

        .help-section-title {
            font-weight: bold;
            color: #BBBBBB;
            margin-bottom: 0.4rem;
            font-size: 0.85rem;
        }

        .help-legend-item {
            display: flex;
            align-items: center;
            margin-bottom: 0.3rem;
            gap: 0.5rem;
        }

        .help-legend-item:last-child {
            margin-bottom: 0;
        }

        .help-legend-symbol {
            min-width: 20px;
            text-align: center;
            font-weight: bold;
        }

        .help-legend-text {
            color: #BBBBBB;
            font-size: 0.8rem;
        }

        .help-close-btn {
            position: absolute;
            top: 0.5rem;
            right: 0.5rem;
            background: none;
            border: none;
            color: #888;
            cursor: pointer;
            font-size: 1rem;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            transition: all 0.2s ease;
        }

        .help-close-btn:hover {
            background: rgba(255, 255, 255, 0.1);
            color: #BBBBBB;
        }

        /* 区域5算法思维日志>和v符号指示器和折叠功能 */
        .grid-area-5 .log-entry {
            position: relative;
        }

        .grid-area-5 .log-arrows {
            margin-left: 6px;
            font-size: 0.6rem; /* 极小字体 */
        }

        .grid-area-5 .arrow-ai-comm,
        .grid-area-5 .arrow-py-ops {
            cursor: pointer;
            font-family: monospace; /* 确保符号显示一致 */
            transition: all 0.2s ease; /* 平滑状态切换 */
        }

        .grid-area-5 .arrow-ai-comm {
            color: #4FC3F7;
            margin-right: 4px; /* 缩小间距 */
        }

        .grid-area-5 .arrow-py-ops {
            color: #81C784;
        }

        /* 收起状态：显示 > */
        .grid-area-5 .arrow-ai-comm:not(.expanded)::before {
            content: ">";
        }

        .grid-area-5 .arrow-py-ops:not(.expanded)::before {
            content: ">";
        }

        /* 展开状态：显示 v */
        .grid-area-5 .arrow-ai-comm.expanded::before {
            content: "v";
        }

        .grid-area-5 .arrow-py-ops.expanded::before {
            content: "v";
        }

        .grid-area-5 .arrow-ai-comm:hover,
        .grid-area-5 .arrow-py-ops:hover {
            opacity: 0.7;
        }

        .grid-area-5 .expanded-details {
            margin-top: 8px;
            padding: 8px;
            background: #1E1F22;
            border-left: 3px solid #0078D4;
            border-radius: 3px;
            font-size: 0.7rem;
            line-height: 1.3;
        }

        .grid-area-5 .ai-comm-details {
            border-left-color: #4FC3F7;
        }

        .grid-area-5 .py-ops-details {
            border-left-color: #81C784;
        }

        .constraint-node.state-machine {
            background: #9C27B0;
            border-color: #9C27B0;
            color: white;
        }

        .constraint-node:hover {
            transform: scale(1.1);
            z-index: 10;
        }

        /* V2风险评分仪表盘 */
        .risk-dashboard {
            background: #2D2D30;
            padding: 0.8rem;
            border-radius: 4px;
            margin-bottom: 0.8rem;
        }

        .risk-score {
            text-align: center;
            margin-bottom: 0.5rem;
        }

        .risk-score-value {
            font-size: 1.5rem;
            font-weight: bold;
        }

        .risk-score.critical .risk-score-value {
            color: #F44336;
        }

        .risk-score.high .risk-score-value {
            color: #FF9800;
        }

        .risk-score.medium .risk-score-value {
            color: #FFC107;
        }

        .risk-score.low .risk-score-value {
            color: #4CAF50;
        }

        .risk-score-label {
            font-size: 0.7rem;
            color: #BBBBBB;
        }

        /* V2四阶段流程进度 */
        .stage-progress {
            margin-bottom: 0.8rem;
        }

        .stage-item {
            display: flex;
            align-items: center;
            margin-bottom: 0.3rem;
            padding: 0.3rem;
            border-radius: 3px;
            transition: background-color 0.2s ease;
        }

        .stage-item.current {
            background: #0078D4;
            color: white;
        }

        .stage-item.completed {
            background: #4CAF50;
            color: white;
        }

        .stage-item.pending {
            background: #666;
            color: #999;
        }

        .stage-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 0.5rem;
        }

        .stage-indicator.current {
            background: #0078D4;
            animation: pulse 2s infinite;
        }

        .stage-indicator.completed {
            background: #4CAF50;
        }

        .stage-indicator.pending {
            background: #666;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        /* V2决策中心卡片样式 */
        .decision-card {
            background: #393B40;
            padding: 0.8rem;
            border-radius: 4px;
            margin-bottom: 0.8rem;
            border-left: 4px solid;
        }

        .decision-card.insight {
            border-left-color: #4CAF50;
        }

        .decision-card.action {
            border-left-color: #FF9800;
        }

        .decision-card.log {
            border-left-color: #2196F3;
        }

        .decision-card-title {
            font-weight: bold;
            margin-bottom: 0.5rem;
        }

        .decision-options {
            display: flex;
            gap: 0.5rem;
            margin-top: 0.5rem;
        }

        .decision-btn {
            padding: 0.4rem 0.8rem;
            background: transparent;
            border: 1px solid #0078D4;
            color: #0078D4;
            border-radius: 3px;
            cursor: pointer;
            font-size: 0.8rem;
            transition: all 0.2s ease;
        }

        .decision-btn:hover {
            background: #0078D4;
            color: white;
        }

        .decision-btn.approve {
            border-color: #4CAF50;
            color: #4CAF50;
        }

        .decision-btn.approve:hover {
            background: #4CAF50;
            color: white;
        }

        .decision-btn.reject {
            border-color: #F44336;
            color: #F44336;
        }

        .decision-btn.reject:hover {
            background: #F44336;
            color: white;
        }

        /* V2输入区域样式 */
        .input-section {
            margin-bottom: 0.8rem;
        }

        .input-field {
            width: 100%;
            background: #3C3F41;
            color: #BBBBBB;
            border: 1px solid #555;
            padding: 0.5rem;
            border-radius: 4px;
            margin-bottom: 0.5rem;
            font-size: 0.9rem;
        }

        .input-field:focus {
            outline: none;
            border-color: #0078D4;
        }

        .action-buttons {
            display: flex;
            gap: 0.5rem;
        }

        .action-btn {
            flex: 1;
            padding: 0.5rem;
            background: transparent;
            border: 1px solid;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.8rem;
            transition: all 0.2s ease;
        }

        .action-btn.primary {
            border-color: #0078D4;
            color: #0078D4;
        }

        .action-btn.primary:hover {
            background: #0078D4;
            color: white;
        }

        .action-btn.secondary {
            border-color: #4CAF50;
            color: #4CAF50;
        }

        .action-btn.secondary:hover {
            background: #4CAF50;
            color: white;
        }

        .action-btn.tertiary {
            border-color: #666;
            color: #666;
        }

        .action-btn.tertiary:hover {
            background: #666;
            color: white;
        }

        .action-btn:disabled {
            opacity: 0.4;
            cursor: not-allowed;
        }

        /* V2项目输出样式 */
        .output-status {
            text-align: center;
            margin-bottom: 0.8rem;
        }

        .output-badge {
            display: inline-block;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-weight: bold;
            font-size: 0.9rem;
        }

        .output-badge.success {
            background: #4CAF50;
            color: white;
        }

        .output-badge.processing {
            background: #FF9800;
            color: white;
            animation: processingPulse 1s infinite;
        }

        .output-badge.error {
            background: #F44336;
            color: white;
        }

        .output-links {
            margin-top: 0.8rem;
        }

        .output-link {
            display: block;
            padding: 0.5rem;
            background: #393B40;
            color: #0078D4;
            text-decoration: none;
            border-radius: 3px;
            margin-bottom: 0.3rem;
            transition: background-color 0.2s ease;
        }

        .output-link:hover {
            background: #0078D4;
            color: white;
        }

        @keyframes processingPulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
    </style>
</head>
<body>
    <!-- 左侧菜单感应区域 -->
    <div class="left-menu-trigger" id="leftMenuTrigger"></div>
    
    <!-- 自动隐藏左侧菜单 -->
    <nav class="left-menu" id="leftMenu">
        <div class="menu-title">🏗️ V2项目经理工作台</div>
        <a href="/" class="menu-item" data-target="home">🏠 主页</a>
        <a href="/nine-grid" class="menu-item" data-target="nine-grid">🔧 九宫格系统</a>
        <a href="/debug" class="menu-item" data-target="debug">🐛 调试中心</a>
        <a href="/api/status" class="menu-item" data-target="status" target="_blank">📊 系统状态</a>
        <a href="/api/health" class="menu-item" data-target="health" target="_blank">❤️ 健康检查</a>
        <div class="menu-item" onclick="openConfigCenter()">⚙️ 配置中心</div>
    </nav>

    <!-- 九宫格主容器 -->
    <div class="nine-grid-container">
        <!-- 区域1-2：项目进度监控 (由组件渲染) -->
        <div id="progress-area" class="grid-area grid-area-1-2 vscode-scrollbar"></div>

        <!-- 区域3：项目风险评估 (由组件渲染) -->
        <div id="risk-area" class="grid-area grid-area-3 vscode-scrollbar"></div>

        <!-- 区域4：项目经理状态 (由组件渲染) -->
        <div id="manager-area" class="grid-area grid-area-4 vscode-scrollbar"></div>

        <!-- 区域5：Python主持人算法思维 (由组件渲染) -->
        <div id="algorithm-area" class="grid-area grid-area-5 vscode-scrollbar"></div>

        <!-- 区域6：项目约束审查 (由组件渲染) -->
        <div id="constraint-area" class="grid-area grid-area-6 vscode-scrollbar"></div>

        <!-- 区域7：项目知识库 (由组件渲染) -->
        <div id="knowledge-area" class="grid-area grid-area-7 vscode-scrollbar"></div>

        <!-- 区域8：人类输入控制区 (由组件渲染) -->
        <div id="control-area" class="grid-area grid-area-8"></div>

        <!-- 区域9：项目交付结果 (由组件渲染) -->
        <div id="deliverables-area" class="grid-area grid-area-9 vscode-scrollbar"></div>
    </div>

    <!-- Unified Architecture Core -->
    <script src="/static/js/unified/http-client.js"></script>
    <script src="/static/js/unified/base-component.js"></script>
    <script src="/static/js/unified/data-manager.js"></script>
    <script src="/static/js/unified/app-manager.js"></script>

    <!-- PM_V2 Data Configuration -->
    <script src="/static/js/pm_v2_data_config.js"></script>

    <!-- PM_V2 Components -->
    <script src="/static/js/components/ProjectProgressComponent.js"></script>
    <script src="/static/js/components/RiskAssessmentComponent.js"></script>
    <script src="/static/js/components/ManagerStatusComponent.js"></script>
    <script src="/static/js/components/AlgorithmMindsetComponent.js"></script>
    <script src="/static/js/components/ConstraintReviewComponent.js"></script>
    <script src="/static/js/components/KnowledgeBaseComponent.js"></script>
    <script src="/static/js/components/HumanInputComponent.js"></script>
    <script src="/static/js/components/ProjectOutputComponent.js"></script>

    <!-- Left Menu JavaScript -->
    <script>
        // 左侧菜单交互逻辑 - 使用更可靠的事件绑定
        function initLeftMenu() {
            const leftMenuTrigger = document.getElementById('leftMenuTrigger');
            const leftMenu = document.getElementById('leftMenu');

            if (!leftMenuTrigger || !leftMenu) {
                console.log('Left menu elements not found, retrying...');
                setTimeout(initLeftMenu, 100);
                return;
            }

            let hideTimeout;
            console.log('Initializing left menu with elements:', leftMenuTrigger, leftMenu);

            // 使用 onmouseenter/onmouseleave 属性方式，更可靠
            leftMenuTrigger.onmouseenter = function() {
                console.log('Mouse entered trigger area!');
                clearTimeout(hideTimeout);
                leftMenu.classList.add('show');
            };

            leftMenu.onmouseenter = function() {
                console.log('Mouse entered menu!');
                clearTimeout(hideTimeout);
                leftMenu.classList.add('show');
            };

            leftMenuTrigger.onmouseleave = function() {
                console.log('Mouse left trigger area!');
                hideTimeout = setTimeout(() => {
                    leftMenu.classList.remove('show');
                }, 300);
            };

            leftMenu.onmouseleave = function() {
                console.log('Mouse left menu!');
                hideTimeout = setTimeout(() => {
                    leftMenu.classList.remove('show');
                }, 300);
            };

            console.log('Left menu initialized successfully');
        }

        // 配置中心点击事件
        window.openConfigCenter = function() {
            alert('配置中心功能开发中...');
        };

        // 在DOM加载完成后和页面加载完成后都尝试初始化
        document.addEventListener('DOMContentLoaded', initLeftMenu);
        window.addEventListener('load', initLeftMenu);
    </script>

    <!-- App Initialization -->
    <script src="/static/js/pm_v2_unified_init.js"></script>
</body>
</html>
