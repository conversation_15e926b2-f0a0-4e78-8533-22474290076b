/**
 * 项目交付结果 (ProjectOutputComponent)
 * 
 * 功能：
 * - 显示整体性审计的状态。
 * - 提供最终产出物的下载链接。
 * - 展示项目完成后的各项处理统计。
 * 
 * 数据依赖：
 * - deliverables: 包含审计状态、产出链接和最终统计数据。
 */
class ProjectOutputComponent extends BaseComponent {
    getDataTypes() {
        return ['deliverables'];
    }

    render() {
        const deliverables = this.getData('deliverables') || {};
        const auditStatus = deliverables.audit_status || 'processing';
        const statusClass = auditStatus.toLowerCase();

        this.container.innerHTML = `
            <div class="area-title">项目交付结果 (Project Output)</div>
            <div class="area-content">
                <!-- 整体性审计状态 -->
                <div class="output-status">
                    <div class="output-badge ${statusClass}" id="audit-status">${deliverables.audit_status_text || '处理中...'}</div>
                </div>

                <!-- 产出链接 -->
                <div class="output-links" id="output-links" style="${statusClass === 'success' ? 'display: block;' : 'display: none;'}">
                    ${(deliverables.links || []).map(link => `<a href="${link.url}" class="output-link">${link.text}</a>`).join('')}
                </div>

                <!-- 完成统计 -->
                <div style="margin-top: 1rem; font-size: 0.8rem;">
                    <div style="margin-bottom: 0.5rem; font-weight: bold;">处理统计:</div>
                    <div style="margin-bottom: 0.5rem;">
                        <div style="display: flex; justify-content: space-between; margin-bottom: 0.2rem;">
                            <span>文档数量</span>
                            <span style="color: #2196F3; font-weight: bold;" id="processed-docs">${deliverables.stats?.processed_docs || '3/5'}</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill info" style="width: 60%;"></div>
                        </div>
                    </div>
                    <div style="margin-bottom: 0.5rem;">
                        <div style="display: flex; justify-content: space-between; margin-bottom: 0.2rem;">
                            <span>约束数量</span>
                            <span style="color: #4CAF50; font-weight: bold;" id="total-constraints">${deliverables.stats?.total_constraints || '25'}</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill success" style="width: 83%;"></div>
                        </div>
                    </div>
                    <div style="margin-bottom: 0.5rem;">
                        <div style="display: flex; justify-content: space-between; margin-bottom: 0.2rem;">
                            <span>风险数量</span>
                            <span style="color: #FF9800; font-weight: bold;" id="total-risks">${deliverables.stats?.total_risks || '2'}</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill warning" style="width: 40%;"></div>
                        </div>
                    </div>
                    <div style="margin-bottom: 0.5rem;">
                        <div style="display: flex; justify-content: space-between; margin-bottom: 0.2rem;">
                            <span>处理时间</span>
                            <span style="color: #9C27B0; font-weight: bold;" id="total-time">${deliverables.stats?.total_time || '150秒'}</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 75%; background: linear-gradient(90deg, #9C27B0, #BA68C8);"></div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    bindEvents() {
        // 事件委托处理动态生成的链接
        this.container.addEventListener('click', (e) => {
            if (e.target.classList.contains('output-link')) {
                e.preventDefault();
                console.log(`产出链接被点击: ${e.target.href}`);
                // 可以在此触发下载或导航事件
                this.triggerEvent('deliverable-clicked', { url: e.target.href, text: e.target.textContent });
            }
        });
    }
}