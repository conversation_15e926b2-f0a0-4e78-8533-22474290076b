/**
 * 项目经理状态 (ManagerStatusComponent)
 * 
 * 功能：
 * - 显示当前项目经理的角色。
 * - 显示当前正在执行的任务。
 * - 显示工作状态和当前处理的文档。
 * - 展示任务进度和已处理时间。
 * 
 * 数据依赖：
 * - manager_status: 包含项目经理的角色、工作状态等。
 * - task_status: 包含当前任务、进度和时间。
 */
class ManagerStatusComponent extends BaseComponent {
    getDataTypes() {
        return ['manager_status', 'task_status'];
    }

    render() {
        const managerStatus = this.getData('manager_status') || {};
        const taskStatus = this.getData('task_status') || {};
        const workStatusColor = (managerStatus.work_status || 'ACTIVE').toLowerCase() === 'active' ? '#4CAF50' : '#FF9800';

        this.container.innerHTML = `
            <div class="area-title">项目经理状态 (PM Status)</div>
            <div class="area-content">
                <div class="status-item">
                    <span class="status-indicator status-active"></span>
                    <strong>当前项目经理:</strong> <span id="current-pm-role">${managerStatus.current_pm_role || '首席架构师AI'}</span>
                </div>
                <div class="status-item">
                    <span class="status-indicator status-thinking"></span>
                    <strong>当前任务:</strong> <span id="current-task">${taskStatus.current_task || '从01号文档中识别设计意图并进行实体分类'}</span>
                </div>
                <div class="status-item">
                    <span class="status-indicator status-active"></span>
                    <strong>工作状态:</strong> <span id="work-status" style="color: ${workStatusColor};">${managerStatus.work_status || 'ACTIVE'}</span>
                </div>
                <div class="status-item">
                    <span class="status-indicator status-converging"></span>
                    <strong>处理文档:</strong> <span id="current-doc">${managerStatus.current_doc || '1-总体架构设计-V2.md'}</span>
                </div>

                <!-- 任务进度 -->
                <div style="margin-top: 1rem;">
                    <div style="display: flex; justify-content: space-between; margin-bottom: 0.2rem;">
                        <span><strong>任务进度:</strong></span>
                        <span style="color: #2196F3; font-weight: bold;">${taskStatus.progress || '75'}%</span>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill info" style="width: ${taskStatus.progress || '75'}%;"></div>
                    </div>
                </div>

                <div class="status-item" style="margin-top: 0.5rem;">
                    <strong>已处理时间:</strong> <span id="processing-time" style="color: #FF9800;">${taskStatus.processing_time || '2分30秒'}</span>
                </div>
            </div>
        `;
    }

    bindEvents() {
        // 纯展示组件，无事件绑定
    }
}