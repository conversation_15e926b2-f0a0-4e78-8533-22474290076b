/**
 * 项目知识库 (KnowledgeBaseComponent)
 * 
 * 功能：
 * - 可视化项目中的原子约束及其关系。
 * - 支持点击约束节点以查看详情。
 * - 提供帮助弹窗解释图例。
 * 
 * 数据依赖：
 * - knowledge_graph: 包含知识图谱的节点和连接数据。
 */
class KnowledgeBaseComponent extends BaseComponent {
    getDataTypes() {
        return ['knowledge_graph'];
    }

    render() {
        const graphData = this.getData('knowledge_graph') || { nodes: [], connections: [] };

        this.container.innerHTML = `
            <div class="area-title">
                项目知识库 (Knowledge Base)
                <span class="help-icon" title="点击查看图例说明">
                    <span class="help-symbol">?</span>
                </span>
            </div>
            <div class="area-content">
                <div class="knowledge-graph" id="knowledge-graph">
                    ${graphData.nodes.map(node => this.renderNode(node)).join('')}
                    ${graphData.connections.map(conn => this.renderConnection(conn, graphData.nodes)).join('')}
                    <div class="node-tooltip" id="node-tooltip"></div>
                </div>
                ${this.renderLegend()}
                ${this.renderHelpPopup()}
            </div>
        `;
    }
    
    renderNode(node) {
        const categoryClass = (node.category || 'constraint').toLowerCase().replace(/_/g, '-');
        const forkedClass = node.is_forked ? 'forked' : '';
        return `
            <div class="constraint-node ${categoryClass} ${forkedClass}" 
                 style="top: ${node.y}px; left: ${node.x}px;"
                 data-id="${node.id}"
                 data-category="${node.category}"
                 title="${node.id}: ${node.description}">
                ${node.id}
                <div class="node-label">${node.label || node.category}</div>
                ${node.is_forked ? `<div class="fork-indicator">↗ ${node.forked_from}</div>` : ''}
            </div>
        `;
    }

    renderConnection(conn, nodes) {
        const fromNode = nodes.find(n => n.id === conn.from);
        const toNode = nodes.find(n => n.id === conn.to);
        if (!fromNode || !toNode) return '';

        const x1 = fromNode.x + 25;
        const y1 = fromNode.y + 25;
        const x2 = toNode.x + 25;
        const y2 = toNode.y + 25;

        const length = Math.sqrt((x2 - x1) ** 2 + (y2 - y1) ** 2);
        const angle = Math.atan2(y2 - y1, x2 - x1) * 180 / Math.PI;
        const connClass = conn.type === 'fork' ? 'fork' : 'reference';

        return `<div class="constraint-connection ${connClass}" style="top: ${y1}px; left: ${x1}px; width: ${length}px; transform: rotate(${angle}deg);"></div>`;
    }
    
    renderLegend() {
        return `
            <div style="margin-top: 0.8rem; font-size: 0.7rem;">
                <div style="font-weight: bold; margin-bottom: 0.5rem; color: #0078D4;">AtomicConstraint类型:</div>
                <div style="margin-bottom: 0.2rem; display: flex; align-items: center;">
                    <span style="color: #FF9800; margin-right: 0.3rem;">●</span>
                    <span>boundary_condition</span>
                </div>
                <div style="margin-bottom: 0.2rem; display: flex; align-items: center;">
                    <span style="color: #4CAF50; margin-right: 0.3rem;">●</span>
                    <span>constraint</span>
                </div>
                <div style="margin-bottom: 0.2rem; display: flex; align-items: center;">
                    <span style="color: #F44336; margin-right: 0.3rem;">●</span>
                    <span>guardrail</span>
                </div>
                <div style="margin-bottom: 0.2rem; display: flex; align-items: center;">
                    <span style="color: #9C27B0; margin-right: 0.3rem;">●</span>
                    <span>state_machine</span>
                </div>
            </div>
        `;
    }

    renderHelpPopup() {
        return `
            <div class="knowledge-help-popup" id="knowledge-help-popup">
                <button class="help-close-btn">×</button>
                <div class="help-popup-title"><span>📚</span><span>知识库图例</span></div>
                <!-- ... 帮助内容 ... -->
            </div>
        `;
    }

    bindEvents() {
        const graph = this.container.querySelector('#knowledge-graph');
        const helpIcon = this.container.querySelector('.help-icon');
        const helpPopup = this.container.querySelector('#knowledge-help-popup');
        const helpCloseBtn = helpPopup.querySelector('.help-close-btn');

        if (graph) {
            graph.addEventListener('click', (e) => {
                const node = e.target.closest('.constraint-node');
                if (node) {
                    const constraintId = node.dataset.id;
                    this.showConstraintDetail(constraintId);
                }
            });
        }

        if (helpIcon) {
            helpIcon.addEventListener('click', () => helpPopup.classList.add('show'));
        }
        
        if (helpCloseBtn) {
            helpCloseBtn.addEventListener('click', () => helpPopup.classList.remove('show'));
        }
    }

    showConstraintDetail(constraintId) {
        const constraintReviewComponent = this.appManager.getComponent('constraint-area');
        if (constraintReviewComponent) {
            const graphData = this.getData('knowledge_graph') || { nodes: [] };
            const nodeData = graphData.nodes.find(n => n.id === constraintId);
            if (nodeData) {
                // 假设节点数据本身就包含足够的详情
                constraintReviewComponent.displayConstraint(nodeData);
            }
        }
    }
}