document.addEventListener('DOMContentLoaded', async () => {
    // 假设 projectId 可以从URL或者某个全局变量中获取
    const projectId = 'default_project';

    const appManager = new AppManager(projectId);

    // 注册所有PM_V2组件 (type, containerId, ComponentClass, config)
    appManager.registerComponent('progress', 'progress-area', ProjectProgressComponent);
    appManager.registerComponent('risk', 'risk-area', RiskAssessmentComponent);
    appManager.registerComponent('manager', 'manager-area', ManagerStatusComponent);
    appManager.registerComponent('algorithm', 'algorithm-area', AlgorithmMindsetComponent);
    appManager.registerComponent('constraint', 'constraint-area', ConstraintReviewComponent);
    appManager.registerComponent('knowledge', 'knowledge-area', KnowledgeBaseComponent);
    appManager.registerComponent('control', 'control-area', HumanInputComponent);
    appManager.registerComponent('deliverables', 'deliverables-area', ProjectOutputComponent);

    try {
        // 初始化所有组件
        await appManager.init();
        console.log('PM_V2 Unified Architecture Initialized Successfully.');

        // 将 appManager 暴露到全局，方便调试
        window.pmV2App = appManager;

        // 如果URL包含test=true，运行测试
        if (window.location.search.includes('test=true')) {
            console.log('Running PM_V2 tests...');
            if (window.PM_V2_Tests) {
                const tests = new PM_V2_Tests();
                await tests.runAllTests();
            } else {
                console.warn('PM_V2_Tests not found. Make sure pm_v2_test.js is loaded.');
            }
        }

    } catch (error) {
        console.error('Failed to initialize PM_V2 Unified Architecture:', error);
    }
});