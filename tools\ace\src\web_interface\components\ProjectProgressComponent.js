/**
 * 项目进度监控 (ProjectProgressComponent)
 * 
 * 功能：
 * - 显示项目四个阶段的总体进度。
 * - 展示阶段零的详细预验证指标。
 * - 统计关键指标，如原子约束、全局契约等。
 * - 显示整体项目完成百分比。
 * 
 * 数据依赖：
 * - progress: 包含整体进度和关键指标的数据。
 * - stage_metrics: 包含阶段零的详细指标。
 */
class ProjectProgressComponent extends BaseComponent {
    getDataTypes() {
        return ['progress', 'stage_metrics'];
    }

    render() {
        const progressData = this.getData('progress') || {};
        const metricsData = this.getData('stage_metrics') || {};

        this.container.innerHTML = `
            <div class="area-title">项目进度监控 (Process Overview)</div>
            <div class="area-content">
                <!-- V4.2四阶段流程进度 -->
                <div class="stage-progress">
                    <div class="stage-item stage-zero-highlight current">
                        <span class="status-indicator status-thinking"></span>
                        <span>阶段零：标准化与预验证</span>
                        <span class="badge badge-reliability">可靠性基石</span>
                    </div>
                    <div class="stage-item pending">
                        <span class="status-indicator status-pending"></span>
                        <span>阶段一：全局契约生成</span>
                    </div>
                    <div class="stage-item pending">
                        <span class="status-indicator status-pending"></span>
                        <span>阶段二：引用式契约生成</span>
                    </div>
                    <div class="stage-item pending">
                        <span class="status-indicator status-pending"></span>
                        <span>阶段三：契约履行与审计</span>
                    </div>
                    <div class="stage-item pending">
                        <span class="status-indicator status-pending"></span>
                        <span>阶段四：整体性审计</span>
                    </div>
                </div>

                <!-- 阶段零专用指标 -->
                <div class="stage-zero-metrics">
                    <div class="metrics-title">
                        <span>🛡️</span>
                        <span>阶段零预验证指标</span>
                    </div>
                    <div class="metric-item">
                        <div style="display: flex; justify-content: space-between; margin-bottom: 0.2rem;">
                            <span class="metric-label">预验证通过率</span>
                            <span class="metric-value success">${metricsData.pre_validation_pass_rate || '100%'}</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill success" style="width: ${metricsData.pre_validation_pass_rate || '100%'};"></div>
                        </div>
                    </div>
                    <div class="metric-item">
                        <div style="display: flex; justify-content: space-between; margin-bottom: 0.2rem;">
                            <span class="metric-label">冲突预防数量</span>
                            <span class="metric-value info">${metricsData.conflict_prevention_count || '3'}</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill info" style="width: 75%;"></div>
                        </div>
                    </div>
                    <div class="metric-item">
                        <div style="display: flex; justify-content: space-between; margin-bottom: 0.2rem;">
                            <span class="metric-label">Schema验证通过</span>
                            <span class="metric-value success">${metricsData.schema_validation_passed || '25'}/${metricsData.schema_validation_total || '25'}</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill success" style="width: 100%;"></div>
                        </div>
                    </div>
                </div>

                <!-- 关键指标统计 -->
                <div style="margin-top: 1rem;">
                    <div style="font-weight: bold; margin-bottom: 0.5rem;">关键指标:</div>
                    <div style="margin-bottom: 0.5rem;">
                        <div style="display: flex; justify-content: space-between; margin-bottom: 0.2rem;">
                            <span>已发现原子约束</span>
                            <span style="color: #4CAF50; font-weight: bold;" id="atomic-constraints-count">${progressData.atomic_constraints_count || '25'}</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill success" style="width: 62.5%;"></div>
                        </div>
                    </div>
                    <div style="margin-bottom: 0.5rem;">
                        <div style="display: flex; justify-content: space-between; margin-bottom: 0.2rem;">
                            <span>已生成全局契约点</span>
                            <span style="color: #2196F3; font-weight: bold;" id="global-contracts-count">${progressData.global_contracts_count || '18'}</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill info" style="width: 72%;"></div>
                        </div>
                    </div>
                    <div style="margin-bottom: 0.5rem;">
                        <div style="display: flex; justify-content: space-between; margin-bottom: 0.2rem;">
                            <span>已处理文档</span>
                            <span style="color: #FF9800; font-weight: bold;"><span id="processed-docs-count">${progressData.processed_docs_count || '3'}</span>/${progressData.total_docs_count || '5'}</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill warning" style="width: 60%;"></div>
                        </div>
                    </div>
                    <div style="margin-bottom: 0.5rem;">
                        <div style="display: flex; justify-content: space-between; margin-bottom: 0.2rem;">
                            <span>当前可靠性评分</span>
                            <span style="color: #4CAF50; font-weight: bold;" id="current-reliability-score">${progressData.current_reliability_score || '87.7%'}</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill success" style="width: 87.7%;"></div>
                        </div>
                    </div>
                </div>

                <!-- 整体进度 -->
                <div style="margin-top: 1rem;">
                    <div style="margin-bottom: 0.5rem; font-weight: bold;">整体进度:</div>
                    <div style="background: #2A2D30; padding: 0.8rem; border-radius: 4px; border: 1px solid #3C3F41;">
                        <div style="display: flex; justify-content: space-between; margin-bottom: 0.3rem;">
                            <span>${progressData.overall_progress_stage || '阶段零进行中'}</span>
                            <span style="color: #0078D4; font-weight: bold;">${progressData.overall_progress_percentage || '25%'}</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill primary" id="overall-progress" style="width: ${progressData.overall_progress_percentage || '25%'};"></div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    bindEvents() {
        // 此组件主要是数据展示，没有复杂的交互事件
    }
}