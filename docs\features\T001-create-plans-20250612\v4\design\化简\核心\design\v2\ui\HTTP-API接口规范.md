# Project Manager V2 HTTP API接口规范

## 概述

本文档定义了Project Manager V2与后端V4.2治理引擎的HTTP REST API接口规范，涵盖项目管理、数据获取、文件操作等核心功能。

## 基础信息

### 服务地址
```
Base URL: http://localhost:25526/api/v2
```

### 认证方式
```http
Authorization: Bearer {jwt_token}
```

### 通用响应格式
```json
{
    "success": true,
    "data": {
        // 响应数据
    },
    "message": "操作成功",
    "timestamp": "2025-01-31T14:17:30Z"
}
```

### 错误响应格式
```json
{
    "success": false,
    "error": {
        "code": "ERROR_CODE",
        "message": "错误描述",
        "details": "详细错误信息"
    },
    "timestamp": "2025-01-31T14:17:30Z"
}
```

---

## 项目管理接口

### 1. 获取项目列表
```http
GET /projects
```

**响应示例：**
```json
{
    "success": true,
    "data": {
        "projects": [
            {
                "id": "123",
                "name": "V4.2架构治理项目",
                "status": "in_progress",
                "created_at": "2025-01-31T10:00:00Z",
                "updated_at": "2025-01-31T14:17:30Z",
                "document_path": "/path/to/docs",
                "progress_percentage": 35
            }
        ],
        "total": 1
    }
}
```

### 2. 创建新项目
```http
POST /projects
```

**请求体：**
```json
{
    "name": "新项目名称",
    "document_path": "/path/to/documents",
    "description": "项目描述"
}
```

**响应示例：**
```json
{
    "success": true,
    "data": {
        "project": {
            "id": "124",
            "name": "新项目名称",
            "status": "created",
            "document_path": "/path/to/documents",
            "created_at": "2025-01-31T14:20:00Z"
        }
    }
}
```

### 3. 获取项目详情
```http
GET /projects/{project_id}
```

**响应示例：**
```json
{
    "success": true,
    "data": {
        "project": {
            "id": "123",
            "name": "V4.2架构治理项目",
            "status": "in_progress",
            "document_path": "/path/to/docs",
            "current_stage": 0,
            "progress_percentage": 35,
            "created_at": "2025-01-31T10:00:00Z",
            "updated_at": "2025-01-31T14:17:30Z",
            "statistics": {
                "documents_total": 5,
                "documents_processed": 3,
                "constraints_generated": 25,
                "risks_prevented": 3
            }
        }
    }
}
```

---

## 区域1-2：项目进度监控接口

### 1. 获取项目进度概览
```http
GET /projects/{project_id}/progress
```

**响应示例：**
```json
{
    "success": true,
    "data": {
        "project_id": "123",
        "current_stage": {
            "stage_number": 0,
            "stage_name": "标准化与预验证",
            "status": "in_progress",
            "progress_percentage": 35,
            "status_indicator": "status-thinking"
        },
        "stage_zero_metrics": {
            "pre_validation_pass_rate": 100,
            "conflict_prevention_count": 3,
            "schema_validation_passed": 25,
            "schema_validation_total": 25
        },
        "key_metrics": {
            "atomic_constraints_discovered": 25,
            "global_contracts_generated": 18,
            "documents_processed": 3,
            "documents_total": 5,
            "current_reliability_score": 87.7
        },
        "overall_progress": {
            "percentage": 35,
            "status": "阶段零进行中",
            "estimated_completion": "2025-01-31T15:00:00Z"
        }
    }
}
```

---

## 区域3：风险评估接口

### 1. 获取风险评估数据
```http
GET /projects/{project_id}/risk-assessment
```

**响应示例：**
```json
{
    "success": true,
    "data": {
        "reliability_score": {
            "value": 87,
            "status": "good",
            "improvement_from_prevention": 22
        },
        "prevented_risks": [
            {
                "id": "risk_001",
                "level": "critical",
                "description": "核心决策逻辑与全局护栏存在原则性冲突",
                "prevention_mechanism": "ConstraintPreprocessor在阶段零已阻止此冲突进入知识库",
                "prevention_status": "prevented",
                "prevented_at": "2025-01-31T14:15:00Z"
            }
        ],
        "document_health_report": {
            "reliability_score": 87,
            "prevented_conflicts": 3,
            "report_url": "/api/v2/projects/123/health-report"
        }
    }
}
```

### 2. 获取文档健康报告
```http
GET /projects/{project_id}/health-report
```

**响应示例：**
```json
{
    "success": true,
    "data": {
        "document_path": "/path/to/docs/1-总体架构设计-V2.md",
        "reliability_score": 0.87,
        "detected_risks": [
            {
                "risk_id": "risk_001",
                "risk_level": "critical",
                "risk_type": "principle_conflict",
                "description": "核心决策逻辑与全局护栏存在原则性冲突",
                "evidence_text": "相关文档片段...",
                "suggestion": "建议修正冲突的设计原则",
                "requires_human_intervention": true
            }
        ],
        "consistency_issues": [],
        "summary": "文档整体质量良好，但存在一个需要人工干预的致命级风险。",
        "generated_at": "2025-01-31T14:17:30Z"
    }
}
```

---

## 区域4：项目经理状态接口

### 1. 获取项目经理状态
```http
GET /projects/{project_id}/manager-status
```

**响应示例：**
```json
{
    "success": true,
    "data": {
        "current_manager": {
            "role": "首席架构师AI",
            "status": "active",
            "status_indicator": "status-active"
        },
        "current_task": {
            "description": "从01号文档中识别设计意图并进行实体分类",
            "status": "in_progress",
            "progress_percentage": 75,
            "status_indicator": "status-thinking",
            "estimated_completion": "2025-01-31T14:25:00Z"
        },
        "work_status": {
            "status": "ACTIVE",
            "color": "#4CAF50"
        },
        "current_document": {
            "name": "1-总体架构设计-V2.md",
            "status": "processing",
            "status_indicator": "status-converging",
            "progress_percentage": 60
        },
        "processing_time": {
            "elapsed_seconds": 150,
            "formatted": "2分30秒",
            "color": "#FF9800",
            "efficiency_status": "normal"
        }
    }
}
```

---

## 区域5：算法思维接口

### 1. 获取算法思维日志
```http
GET /projects/{project_id}/algorithm-logs
```

**查询参数：**
- `limit`: 返回条目数量限制 (默认50)
- `offset`: 偏移量 (默认0)
- `since`: 获取指定时间之后的日志

**响应示例：**
```json
{
    "success": true,
    "data": {
        "ai_algorithm_collaboration": {
            "ai_role": "首席架构师AI",
            "algorithm_component": "ConstraintPreprocessor",
            "current_status": "pre_validation_passed",
            "result": "已生成原子约束 GB001"
        },
        "process_logs": [
            {
                "id": "startup_check_ide",
                "timestamp": "14:17:30",
                "message": "启动检查: 正在验证IDE AI连接状态...✅ 连接正常",
                "expandable": true,
                "ai_comm_details": {
                    "request": "IDE AI连接状态检查",
                    "response_time_ms": 23,
                    "status": "连接正常",
                    "tools_count": 6
                },
                "py_ops_details": {
                    "operation": "验证IDE连接",
                    "execution_time_ms": 12,
                    "memory_usage_mb": 2.3,
                    "cpu_usage_percent": 0.1
                }
            }
        ],
        "total": 25,
        "has_more": true
    }
}
```

### 2. 获取日志详情
```http
GET /projects/{project_id}/algorithm-logs/{log_id}
```

**响应示例：**
```json
{
    "success": true,
    "data": {
        "log_entry": {
            "id": "startup_check_ide",
            "timestamp": "14:17:30",
            "message": "启动检查: 正在验证IDE AI连接状态...✅ 连接正常",
            "ai_comm_details": {
                "request": "IDE AI连接状态检查",
                "response_time_ms": 23,
                "status": "连接正常",
                "tools_count": 6,
                "request_payload": "...",
                "response_payload": "..."
            },
            "py_ops_details": {
                "operation": "验证IDE连接",
                "execution_time_ms": 12,
                "memory_usage_mb": 2.3,
                "cpu_usage_percent": 0.1,
                "stack_trace": "...",
                "environment_info": "..."
            }
        }
    }
}
```

---

## 区域6：约束审查接口

### 1. 获取约束列表
```http
GET /projects/{project_id}/constraints
```

**查询参数：**
- `category`: 约束类别过滤
- `parent_id`: 父约束ID过滤
- `limit`: 返回数量限制
- `offset`: 偏移量

**响应示例：**
```json
{
    "success": true,
    "data": {
        "constraints": [
            {
                "id": "GB001",
                "category": "boundary_condition",
                "type": "response_time",
                "description": "所有面向用户的API必须在1000ms内响应",
                "parent_id": null,
                "children_count": 2,
                "created_at": "2025-01-31T14:17:30Z"
            }
        ],
        "total": 25,
        "has_more": true
    }
}
```

### 2. 获取约束详细信息
```http
GET /projects/{project_id}/constraints/{constraint_id}
```

**响应示例：**
```json
{
    "success": true,
    "data": {
        "constraint": {
            "id": "GB001",
            "category": "boundary_condition",
            "type": "response_time",
            "params": {
                "target_entity": "api_request",
                "max_ms": 1000,
                "unit": "milliseconds",
                "on_exceed_action": "return_http_429"
            },
            "description": "所有面向用户的API必须在1000ms内响应",
            "source_block_id": "block_001",
            "parent_id": null,
            "created_at": "2025-01-31T14:17:30Z",
            "updated_at": "2025-01-31T14:17:30Z",
            "lineage": {
                "children_count": 2,
                "children_ids": ["LB002", "BC003"],
                "ancestors": [],
                "descendants": ["LB002", "BC003", "LC005"]
            },
            "validation_status": {
                "schema_valid": true,
                "conflict_check": "passed",
                "parameter_range": "valid",
                "last_validated": "2025-01-31T14:17:31Z"
            }
        }
    }
}
```

---

## 区域7：知识库可视化接口

### 1. 获取知识库图谱数据
```http
GET /projects/{project_id}/knowledge-graph
```

**查询参数：**
- `layout`: 布局算法 (auto, force, hierarchical)
- `include_connections`: 是否包含连接线数据

**响应示例：**
```json
{
    "success": true,
    "data": {
        "nodes": [
            {
                "id": "GB001",
                "category": "boundary_condition",
                "type": "response_time",
                "position": { "x": 30, "y": 30 },
                "parent_id": null,
                "is_forked": false,
                "description": "GB001: 全局API响应时间边界条件",
                "status": "validated"
            },
            {
                "id": "LB002",
                "category": "constraint",
                "type": "user_service",
                "position": { "x": 120, "y": 80 },
                "parent_id": "GB001",
                "is_forked": true,
                "description": "LB002: 用户服务约束(引用式分叉)",
                "status": "validated"
            }
        ],
        "connections": [
            {
                "from": "GB001",
                "to": "LB002",
                "type": "fork",
                "style": "dashed",
                "strength": 0.8
            }
        ],
        "layout_info": {
            "algorithm": "force",
            "bounds": { "width": 300, "height": 200 },
            "generated_at": "2025-01-31T14:17:30Z"
        }
    }
}
```

### 2. 更新节点位置
```http
PUT /projects/{project_id}/knowledge-graph/nodes/{node_id}/position
```

**请求体：**
```json
{
    "position": { "x": 150, "y": 100 }
}
```

---

## 区域8：控制操作接口

### 1. 开始项目处理
```http
POST /projects/{project_id}/actions/start
```

**请求体：**
```json
{
    "stage": "all",  // 或指定阶段: "stage_0", "stage_1", etc.
    "options": {
        "force_restart": false,
        "skip_validation": false
    }
}
```

### 2. 暂停项目处理
```http
POST /projects/{project_id}/actions/pause
```

### 3. 停止项目处理
```http
POST /projects/{project_id}/actions/stop
```

**请求体：**
```json
{
    "force": false,  // 是否强制停止
    "save_progress": true  // 是否保存当前进度
}
```

### 4. 扫描文档
```http
POST /projects/{project_id}/actions/scan
```

**请求体：**
```json
{
    "document_path": "/path/to/documents",
    "recursive": true,
    "file_patterns": ["*.md", "*.txt"]
}
```

**响应示例：**
```json
{
    "success": true,
    "data": {
        "scan_result": {
            "documents_found": 7,
            "new_documents": 2,
            "updated_documents": 1,
            "scan_duration_ms": 150,
            "next_action": "start_processing"
        }
    }
}
```

---

## 区域9：交付结果接口

### 1. 获取交付结果
```http
GET /projects/{project_id}/deliverables
```

**响应示例：**
```json
{
    "success": true,
    "data": {
        "output_files": [
            {
                "id": "file_123",
                "name": "约束规范文档.md",
                "type": "constraint_spec",
                "size": "2.3MB",
                "status": "completed",
                "download_url": "/api/v2/files/file_123/download",
                "generated_at": "2025-01-31T14:19:30Z"
            }
        ],
        "processing_statistics": {
            "documents_processed": { "current": 3, "total": 5, "percentage": 60 },
            "constraints_generated": { "count": 25, "percentage": 83 },
            "risks_identified": { "count": 2, "percentage": 40 },
            "processing_time": { "seconds": 150, "formatted": "2分30秒", "percentage": 75 }
        },
        "completion_status": {
            "overall_progress": 60,
            "estimated_completion": "2025-01-31T15:00:00Z",
            "can_download": true
        }
    }
}
```

### 2. 下载文件
```http
GET /files/{file_id}/download
```

**响应头：**
```http
Content-Type: application/octet-stream
Content-Disposition: attachment; filename="约束规范文档.md"
Content-Length: 2411520
```

---

## 系统状态接口

### 1. 系统健康检查
```http
GET /health
```

**响应示例：**
```json
{
    "success": true,
    "data": {
        "status": "healthy",
        "version": "v4.2.1",
        "uptime": "2h 15m 30s",
        "components": {
            "database": "healthy",
            "websocket": "healthy",
            "file_system": "healthy",
            "ai_service": "healthy"
        },
        "performance": {
            "cpu_usage": "15%",
            "memory_usage": "45%",
            "disk_usage": "30%"
        }
    }
}
```

### 2. 系统状态
```http
GET /status
```

**响应示例：**
```json
{
    "success": true,
    "data": {
        "active_projects": 3,
        "total_projects": 15,
        "active_connections": 5,
        "processing_queue": 2,
        "system_load": {
            "cpu": 15.5,
            "memory": 45.2,
            "disk": 30.1
        },
        "last_updated": "2025-01-31T14:17:30Z"
    }
}
```

---

## 错误码定义

### 客户端错误 (4xx)
- `400` - `INVALID_REQUEST` - 请求参数错误
- `401` - `UNAUTHORIZED` - 认证失败
- `403` - `FORBIDDEN` - 权限不足
- `404` - `NOT_FOUND` - 资源不存在
- `409` - `CONFLICT` - 资源冲突
- `422` - `VALIDATION_ERROR` - 数据验证失败

### 服务器错误 (5xx)
- `500` - `INTERNAL_ERROR` - 服务器内部错误
- `502` - `SERVICE_UNAVAILABLE` - 依赖服务不可用
- `503` - `OVERLOADED` - 服务过载
- `504` - `TIMEOUT` - 请求超时

### 业务错误码
- `PROJECT_NOT_FOUND` - 项目不存在
- `CONSTRAINT_VALIDATION_FAILED` - 约束验证失败
- `DOCUMENT_PARSE_ERROR` - 文档解析错误
- `PROCESSING_IN_PROGRESS` - 处理正在进行中
- `INSUFFICIENT_PERMISSIONS` - 权限不足

---

## 性能和限制

### 请求限制
- 每分钟最多100个请求
- 单次上传文件最大100MB
- 单次API响应最大1MB

### 超时设置
- 标准API请求: 30秒
- 文件上传: 5分钟
- 长时间处理: 30分钟

### 缓存策略
- 静态数据缓存: 5分钟
- 动态数据缓存: 30秒
- 文件下载缓存: 1小时
