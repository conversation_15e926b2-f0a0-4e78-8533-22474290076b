/**
 * Python主持人算法思维 (AlgorithmMindsetComponent)
 * 
 * 功能：
 * - 展示AI与算法的协同状态。
 * - 动态渲染算法执行日志。
 * - 支持点击日志条目查看详细信息。
 * - 支持展开/折叠日志的AI通讯和Python操作详情。
 * 
 * 数据依赖：
 * - algorithm_logs: 包含AI-算法协同状态和日志条目。
 */
class AlgorithmMindsetComponent extends BaseComponent {
    getDataTypes() {
        return ['algorithm_logs'];
    }

    render() {
        const logData = this.getData('algorithm_logs') || { collaboration: {}, logs: [] };
        const collaboration = logData.collaboration || {};
        const logs = logData.logs || [];

        this.container.innerHTML = `
            <div class="area-title">Python主持人算法思维</div>
            <div class="area-content" style="height: calc(100% - 2rem); overflow-y: auto;">
                <!-- V4.2 AI-算法协同展示 -->
                <div class="ai-algorithm-collaboration-compact">
                    <div class="collaboration-header-compact">
                        <div class="ai-role-compact">
                            <span class="role-icon">🤖</span>
                            <span>${collaboration.ai_role || '首席架构师AI'}</span>
                        </div>
                        <div class="collaboration-arrow">→</div>
                        <div class="algorithm-role-compact">
                            <span class="role-icon">⚙️</span>
                            <span>${collaboration.algorithm_role || 'ConstraintPreprocessor'}</span>
                        </div>
                    </div>
                    <div class="validation-status-compact">
                        <div class="check-item success">
                            <span class="check-icon">✓</span>
                            <span>${collaboration.status_text || '预验证通过 - 已生成GB001'}</span>
                        </div>
                    </div>
                </div>

                <!-- V4扫描日志预留位置 -->
                <div class="scanning-logs" style="display:none;">扫描日志将在此显示</div>

                <!-- Python主持人算法思维过程 -->
                <div id="process-log" style="font-family: monospace; font-size: 0.8rem; margin-bottom: 1rem;">
                    ${logs.map(log => this.renderLogEntry(log)).join('')}
                </div>
            </div>
        `;
    }
    
    renderLogEntry(log) {
        return `
            <div class="log-entry expandable" data-log-id="${log.id}" style="cursor: pointer; padding: 2px 4px; border-radius: 3px; transition: background-color 0.2s;" onmouseover="this.style.backgroundColor='#393B40'" onmouseout="this.style.backgroundColor='transparent'">
                [${log.timestamp || '14:17:30'}] ${log.message || '启动检查: 正在验证IDE AI连接状态...✅ 连接正常'}
                <span class="log-arrows">
                    <span class="arrow-ai-comm" data-log-id="${log.id}" data-detail-type="ai-comm"></span>
                    <span class="arrow-py-ops" data-log-id="${log.id}" data-detail-type="py-ops"></span>
                </span>
            </div>
        `;
    }

    bindEvents() {
        const processLog = this.container.querySelector('#process-log');
        if (!processLog) return;

        processLog.addEventListener('click', (e) => {
            const logEntry = e.target.closest('.log-entry');
            if (!logEntry) return;

            const logId = logEntry.dataset.logId;

            if (e.target.classList.contains('arrow-ai-comm') || e.target.classList.contains('arrow-py-ops')) {
                // 点击的是箭头
                const detailType = e.target.dataset.detailType;
                this.toggleLogDetail(e.target, detailType, logId);
            } else {
                // 点击的是日志条目本身
                this.showLogDetail(logId);
            }
        });
    }

    showLogDetail(logId) {
        // 安全检查appManager是否存在
        if (!this.appManager) {
            console.warn('AppManager not available in AlgorithmMindsetComponent');
            return;
        }
        
        const humanInputComponent = this.appManager.getComponent('control');
        if (humanInputComponent && typeof humanInputComponent.updateDetail === 'function') {
            const logData = this.getData('algorithm_logs')?.logs.find(l => l.id === logId);
            if (logData) {
                // 根据日志类型生成不同的标题和图标
                let title = '';
                let icon = '';
                
                if (logData.type === 'ai_communication') {
                    title = '🤖 AI通信详情';
                    icon = '🤖';
                } else if (logData.type === 'algorithm_processing') {
                    title = '⚙️ 算法处理详情';
                    icon = '⚙️';
                } else {
                    title = '📋 日志详情';
                    icon = '📋';
                }
                
                const content = `
                    <div style="margin-bottom: 1rem;">
                        <div style="font-size: 1.1rem; font-weight: bold; color: #0078D4; margin-bottom: 0.5rem;">
                            ${icon} ${title}
                        </div>
                        <div style="margin-bottom: 0.8rem;">
                            <strong style="color: #BBBBBB;">检查项目：</strong>
                            <span style="color: #BBBBBB;">${logData.summary}</span>
                        </div>
                        <div style="margin-bottom: 0.8rem;">
                            <strong style="color: #BBBBBB;">执行算法：</strong>
                            <span style="color: #BBBBBB;">AI通信与Python算法协同处理</span>
                            <ul style="margin: 0.3rem 0; padding-left: 1.5rem; color: #BBBBBB;">
                                <li>AI分析：${logData.ai_comm_detail}</li>
                                <li>Python处理：${logData.py_ops_detail}</li>
                            </ul>
                        </div>
                        <div style="margin-bottom: 0.8rem;">
                            <strong style="color: #BBBBBB;">检查结果：</strong>
                            <div style="color: #BBBBBB;">
                                <div>• 处理状态：完成</div>
                                <div>• 时间戳：${logData.timestamp}</div>
                                <div>• 日志ID：${logData.id}</div>
                            </div>
                        </div>
                        <div style="margin-bottom: 0.8rem;">
                            <strong style="color: #BBBBBB;">技术细节：</strong>
                            <div style="color: #BBBBBB;">
                                <div>• AI通信：基于语义分析的智能处理</div>
                                <div>• Python算法：结构化数据处理和存储</div>
                                <div>• 协同机制：AI决策 + Python执行</div>
                            </div>
                        </div>
                        <div style="margin-bottom: 0.8rem;">
                            <strong style="color: #BBBBBB;">性能指标：</strong>
                            <div style="color: #BBBBBB;">
                                <div>• 处理时间：< 1秒</div>
                                <div>• 成功率：100%</div>
                                <div>• 数据完整性：已验证</div>
                            </div>
                        </div>
                    </div>
                `;
                humanInputComponent.updateDetail(content);
            } else {
                humanInputComponent.updateDetail(`未找到ID为 ${logId} 的日志详情。`);
            }
        } else {
            console.warn('control-area component not found or updateDetail method not available');
        }
    }

    toggleLogDetail(arrowElement, detailType, logId) {
        arrowElement.classList.toggle('expanded');
        const logEntry = arrowElement.closest('.log-entry');
        const existingDetail = logEntry.querySelector(`.expanded-details.${detailType}-details`);

        if (existingDetail) {
            existingDetail.remove();
        } else {
            const detailContent = this.getDetailContent(logId, detailType);
            const detailDiv = document.createElement('div');
            detailDiv.className = `expanded-details ${detailType}-details`;
            detailDiv.innerHTML = detailContent;
            logEntry.appendChild(detailDiv);
        }
    }
    
    getDetailContent(logId, type) {
        // 在实际应用中，这里应该从数据中获取详细信息
        return `这是 ${logId} 的 ${type} 详细信息。`;
    }
}